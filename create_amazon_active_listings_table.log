Starting table creation at 2025-07-19T00:09:02.895Z
Environment variables loaded
Supabase credentials found
Supabase client created
Checking if table exists...
Table does not exist
Reading SQL file...
Attempting to create table...
Executing statement 3 of 29...
Error executing statement 3: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 4 of 29...
<PERSON>rror executing statement 4: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 5 of 29...
Error executing statement 5: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 6 of 29...
Error executing statement 6: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 7 of 29...
Error executing statement 7: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 8 of 29...
Error executing statement 8: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 10 of 29...
Error executing statement 10: syntax error at or near "RETURN"
Executing statement 11 of 29...
<PERSON><PERSON>r executing statement 11: EXECUTE of transaction commands is not implemented
Executing statement 12 of 29...
<PERSON>rror executing statement 12: unterminated dollar-quoted string at or near "$$ LANGUAGE plpgsql;"
Executing statement 13 of 29...
Error executing statement 13: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 15 of 29...
Error executing statement 15: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 16 of 29...
Error executing statement 16: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 17 of 29...
Error executing statement 17: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 18 of 29...
Error executing statement 18: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 19 of 29...
Error executing statement 19: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 20 of 29...
Error executing statement 20: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 21 of 29...
Error executing statement 21: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 23 of 29...
Error executing statement 23: EXECUTE of transaction commands is not implemented
Executing statement 24 of 29...
Error executing statement 24: unterminated dollar-quoted string at or near "$$ LANGUAGE plpgsql;"
Executing statement 26 of 29...
Error executing statement 26: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 27 of 29...
Error executing statement 27: relation "public.it_amaz_active_listings_report" does not exist
Executing statement 28 of 29...
Error executing statement 28: relation "public.it_amaz_active_listings_report_id_seq" does not exist
Executing statement 29 of 29...
Error executing statement 29: relation "public.it_amaz_active_listings_report_id_seq" does not exist
Table creation completed
Verifying table creation...
Error verifying table: relation "public.it_amaz_active_listings_report" does not exist
Failed to verify table creation
