-- Function to enqueue a task when t_discs.g_pull is updated
CREATE OR REPLACE FUNCTION fn_enqueue_update_veeqo_d_title_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if:
    -- 1. g_pull has actually changed
    -- 2. shopify_uploaded_at is not null (disc is on Shopify/Veeqo)
    -- 3. sold_date is null (disc is unsold)
    IF (OLD.g_pull IS DISTINCT FROM NEW.g_pull) AND
       (NEW.shopify_uploaded_at IS NOT NULL) AND
       (NEW.sold_date IS NULL) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at,
            enqueued_by
        ) VALUES (
            'update_veeqo_d_title',
            jsonb_build_object(
                'id', NEW.id,
                'g_pull', NEW.g_pull,
                'old_g_pull', OLD.g_pull
            ),
            'pending',
            NOW() + INTERVAL '5 minutes',
            NOW(),
            't_discs g_pull_update_trigger_' || NEW.id
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_enqueue_update_veeqo_d_title ON t_discs;

CREATE TRIGGER trg_enqueue_update_veeqo_d_title
AFTER UPDATE OF g_pull ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_update_veeqo_d_title_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Veeqo title update trigger created for t_discs.g_pull changes';
END $$;
