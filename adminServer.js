// adminServer.js

// Suppress Node.js deprecation warnings
process.noDeprecation = true;

import express from 'express';
import { spawn, exec } from 'child_process';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import os from 'os';
import fs from 'fs';
import XLSX from 'xlsx';
import ExcelJS from 'exceljs';
import { registerInformedApiEndpoints } from './informedApiHandler.js';
import { importDiscsFromGoogleSheets } from './googleSheetsImporter.js';
import { getOldestEmptyMpsCollections, cleanupOldMpsCollections } from './cleanupOldMpsCollections.js';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
// Increase the JSON payload limit to handle large CSV uploads (50MB)
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(express.static(__dirname));

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
let supabase;

if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey);
}

// Helper function to fetch all records using chunked queries to avoid Supabase limits
async function fetchAllRecordsChunked(supabaseClient, tableName, selectFields, orderBy = []) {
  const allRecords = [];
  let offset = 0;
  const chunkSize = 1000;
  let hasMore = true;

  while (hasMore) {
    console.log(`[fetchAllRecordsChunked] Fetching ${tableName} chunk starting at offset ${offset}...`);

    // Build the query
    let query = supabaseClient
      .from(tableName)
      .select(selectFields)
      .range(offset, offset + chunkSize - 1);

    // Apply ordering if specified
    if (orderBy && orderBy.length > 0) {
      orderBy.forEach(order => {
        query = query.order(order.column, order.options || {});
      });
    }

    const { data, error } = await query;

    if (error) {
      console.error(`[fetchAllRecordsChunked] Error fetching ${tableName} chunk:`, error);
      throw error;
    }

    if (!data || data.length === 0) {
      hasMore = false;
      break;
    }

    allRecords.push(...data);
    console.log(`[fetchAllRecordsChunked] Fetched ${data.length} records in this chunk. Total so far: ${allRecords.length}`);

    // If we got fewer records than the chunk size, we've reached the end
    if (data.length < chunkSize) {
      hasMore = false;
    } else {
      offset += chunkSize;
    }
  }

  console.log(`[fetchAllRecordsChunked] Completed fetching ${allRecords.length} total records from ${tableName}`);
  return allRecords;
}

// Function to get local IP address for LAN access
function getLocalIPAddress() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
            if (iface.family === 'IPv4' && !iface.internal) {
                return iface.address;
            }
        }
    }
    return 'localhost';
}

// Track worker process
let workerProcess = null;
let workerOutput = [];
let workerStatus = 'stopped';
let lastRunTime = null;

// Function to update worker output and ensure status is correct
function updateWorkerOutput(output) {
  // Add the output to the worker output array
  if (Array.isArray(output)) {
    workerOutput.push(...output);
  } else {
    workerOutput.push(output);
  }

  // If we're receiving output, the worker must be running
  // This ensures the status stays in sync with the console output
  if (workerOutput.length > 0 && (workerProcess || workerStatus === 'running')) {
    workerStatus = 'running';
  }
}

// API endpoint to get worker status
app.get('/api/worker/status', (req, res) => {
  // Query the database for pending and future tasks
  const getPendingTasksCount = async () => {
    if (!supabase) return 0;

    try {
      const { count, error } = await supabase
        .from('t_task_queue')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
        .lte('scheduled_at', new Date().toISOString());

      if (error) {
        console.error('[adminServer] Error counting pending tasks:', error);
        return 0;
      }

      return count || 0;
    } catch (err) {
      console.error('[adminServer] Exception counting pending tasks:', err);
      return 0;
    }
  };

  const getFutureTasksCount = async () => {
    if (!supabase) return 0;

    try {
      const { count, error } = await supabase
        .from('t_task_queue')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
        .gt('scheduled_at', new Date().toISOString());

      if (error) {
        console.error('[adminServer] Error counting future tasks:', error);
        return 0;
      }

      return count || 0;
    } catch (err) {
      console.error('[adminServer] Exception counting future tasks:', err);
      return 0;
    }
  };

  // Execute both queries in parallel
  Promise.all([getPendingTasksCount(), getFutureTasksCount()])
    .then(([pendingTasksCount, futureTasksCount]) => {
      res.json({
        status: workerStatus,
        pendingTasksCount,
        futureTasksCount,
        lastRunTime,
        output: workerOutput
      });
    })
    .catch(err => {
      console.error('[adminServer] Error getting worker status:', err);
      res.status(500).json({ error: err.message });
    });
});

// API endpoint to get task statistics by type
app.get('/api/tasks/by-type', async (req, res) => {
  if (!supabase) {
    return res.status(500).json({ error: 'Supabase client not initialized' });
  }

  try {
    // Get current timestamp for comparison
    const now = new Date().toISOString();
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();

    // Get all tasks
    const { data: allTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('task_type, status, scheduled_at, processed_at');

    if (tasksError) {
      console.error('[adminServer] Error getting tasks:', tasksError);
      return res.status(500).json({ error: tasksError.message });
    }

    // Process tasks into categories
    const pendingTasks = [];
    const futureTasks = [];
    const completedTasks = [];
    const errorTasks = [];

    allTasks.forEach(task => {
      if (task.status === 'pending' && new Date(task.scheduled_at) <= new Date(now)) {
        pendingTasks.push(task);
      } else if (task.status === 'pending' && new Date(task.scheduled_at) > new Date(now)) {
        futureTasks.push(task);
      } else if (task.status === 'complete' && task.processed_at && new Date(task.processed_at) > new Date(thirtyMinutesAgo)) {
        completedTasks.push(task);
      } else if (task.status === 'error' && task.processed_at && new Date(task.processed_at) > new Date(thirtyMinutesAgo)) {
        errorTasks.push(task);
      } else if (task.status === 'complete') {
        // Include all completed tasks if they don't have processed_at
        completedTasks.push(task);
      } else if (task.status === 'error') {
        // Include all error tasks if they don't have processed_at
        errorTasks.push(task);
      }
    });

    // Count tasks by type
    const pendingTasksByType = {};
    const futureTasksByType = {};
    const completedTasksByType = {};
    const errorTasksByType = {};

    pendingTasks.forEach(task => {
      pendingTasksByType[task.task_type] = (pendingTasksByType[task.task_type] || 0) + 1;
    });

    futureTasks.forEach(task => {
      futureTasksByType[task.task_type] = (futureTasksByType[task.task_type] || 0) + 1;
    });

    completedTasks.forEach(task => {
      completedTasksByType[task.task_type] = (completedTasksByType[task.task_type] || 0) + 1;
    });

    errorTasks.forEach(task => {
      errorTasksByType[task.task_type] = (errorTasksByType[task.task_type] || 0) + 1;
    });

    res.json({
      taskTypes: {
        pending: pendingTasksByType,
        future: futureTasksByType,
        completed: completedTasksByType,
        error: errorTasksByType
      }
    });
  } catch (err) {
    console.error('[adminServer] Exception getting tasks by type:', err);
    res.status(500).json({ error: err.message });
  }
});

// API endpoint to delete wrong Veeqo records
app.post('/api/delete-wrong-veeqo-records', (req, res) => {
  console.log('[adminServer] Running deleteWrongVeeqoRecords.js');

  // Get the limit parameter from the request body
  const limit = req.body.limit;
  console.log(`[adminServer] Limit parameter: ${limit}`);

  try {
    // Run the delete script with the limit parameter if provided
    const scriptArgs = ['deleteWrongVeeqoRecords.js'];
    if (limit) {
      scriptArgs.push(`--limit=${limit}`);
    }

    const deleteProcess = spawn('node', scriptArgs);

    let stdout = '';
    let stderr = '';
    let summary = null;

    deleteProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[deleteWrongVeeqoRecords.js] ${output.trim()}`);

      // Try to extract summary from the output
      const summaryMatch = output.match(/Total records processed: (\d+)[\s\S]*?Successful deletions: (\d+)[\s\S]*?Failed deletions: (\d+)/);

      if (summaryMatch) {
        summary = {
          totalProcessed: parseInt(summaryMatch[1]),
          successCount: parseInt(summaryMatch[2]),
          failureCount: parseInt(summaryMatch[3])
        };
      }
    });

    deleteProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[deleteWrongVeeqoRecords.js] ${output.trim()}`);
    });

    deleteProcess.on('close', (code) => {
      console.log(`[adminServer] deleteWrongVeeqoRecords.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Wrong Veeqo records deleted successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to delete wrong Veeqo records',
          stdout,
          stderr
        });
      }
    });

    deleteProcess.on('error', (err) => {
      console.error('[adminServer] Error running delete script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception deleting wrong Veeqo records: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to run the worker once
app.post('/api/worker/run-once', (req, res) => {
  console.log('[adminServer] Running worker once');

  try {
    // Clear previous output
    workerOutput = [];

    // Kill any existing worker process
    if (workerProcess) {
      console.log('[adminServer] Killing existing worker process');
      workerProcess.kill();
      workerProcess = null;
    }

    // Set the last run time
    lastRunTime = new Date().toISOString();

    // Spawn the worker process in single-run mode
    workerProcess = spawn('node', ['taskQueueWorker.js'], {
      env: {
        ...process.env,
        DAEMON_MODE: 'false'  // Run once and exit
      }
    });
    workerStatus = 'running';

    // Collect stdout
    workerProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(output);
    });

    // Collect stderr
    workerProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(`ERROR: ${output}`);
    });

    // Handle process completion
    workerProcess.on('close', (code) => {
      console.log(`[adminServer] Worker process exited with code ${code}`);
      workerStatus = 'stopped';
      workerProcess = null;
    });

    // Handle process error
    workerProcess.on('error', (err) => {
      console.error('[adminServer] Error spawning worker process:', err);
      updateWorkerOutput(`ERROR: Failed to start worker: ${err.message}`);
      workerStatus = 'stopped';
      workerProcess = null;

      res.status(500).json({
        success: false,
        error: err.message
      });
    });

    // Return success immediately, don't wait for process to complete
    res.json({
      success: true,
      message: 'Worker started successfully'
    });
  } catch (err) {
    console.error(`[adminServer] Exception running worker: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to start the worker daemon
app.post('/api/worker/start-daemon', (req, res) => {
  console.log('[adminServer] Starting worker daemon');

  try {
    // Clear previous output
    workerOutput = [];

    // Kill any existing worker process
    if (workerProcess) {
      console.log('[adminServer] Killing existing worker process');
      workerProcess.kill();
      workerProcess = null;
    }

    // Set the last run time
    lastRunTime = new Date().toISOString();

    // Spawn the worker process in daemon mode with 15-second intervals
    workerProcess = spawn('node', ['taskQueueWorker.js'], {
      env: {
        ...process.env,
        DAEMON_MODE: 'true',
        POLL_INTERVAL_MS: '15000'  // 15 seconds as you prefer
      }
    });
    workerStatus = 'running';

    // Collect stdout
    workerProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(output);
    });

    // Collect stderr
    workerProcess.stderr.on('data', (data) => {
      const output = data.toString();
      console.error(`[taskQueueWorker.js] ${output.trim()}`);
      updateWorkerOutput(`ERROR: ${output}`);
    });

    // Handle process completion
    workerProcess.on('close', (code) => {
      console.log(`[adminServer] Worker daemon process exited with code ${code}`);
      workerStatus = 'stopped';
      workerProcess = null;
    });

    // Handle process error
    workerProcess.on('error', (err) => {
      console.error('[adminServer] Error spawning worker daemon process:', err);
      updateWorkerOutput(`ERROR: Failed to start worker daemon: ${err.message}`);
      workerStatus = 'stopped';
      workerProcess = null;

      res.status(500).json({
        success: false,
        error: err.message
      });
    });

    // Return success immediately, don't wait for process to complete
    res.json({
      success: true,
      message: 'Worker daemon started successfully'
    });
  } catch (err) {
    console.error(`[adminServer] Exception starting worker daemon: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to stop the worker daemon
app.post('/api/worker/stop-daemon', (req, res) => {
  console.log('[adminServer] Stopping worker daemon');

  try {
    if (workerProcess) {
      console.log('[adminServer] Killing worker process');
      workerProcess.kill();
      workerProcess = null;
      workerStatus = 'stopped';
      updateWorkerOutput('Worker daemon stopped by user');

      res.json({
        success: true,
        message: 'Worker daemon stopped successfully'
      });
    } else {
      console.log('[adminServer] No worker process to stop');
      res.json({
        success: true,
        message: 'No worker daemon running'
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception stopping worker daemon: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import Veeqo sellables
app.post('/api/import-veeqo-sellables', async (req, res) => {
  console.log('[adminServer] Truncating imported_table_veeqo_sellables_export and running import_veeqo_sellables.js');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // First, truncate the table
    const { error: truncateError } = await supabase.rpc('truncate_imported_table_veeqo_sellables_export');

    if (truncateError) {
      console.error(`[adminServer] Error truncating table: ${truncateError.message}`);
      return res.status(500).json({
        success: false,
        error: truncateError.message
      });
    }

    console.log('[adminServer] Table truncated successfully, running import script...');

    // Run the import script
    const importProcess = spawn('node', ['import_veeqo_sellables.js']);

    let stdout = '';
    let stderr = '';

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[import_veeqo_sellables.js] ${output.trim()}`);
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[import_veeqo_sellables.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] import_veeqo_sellables.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Veeqo sellables imported successfully',
          details: {
            stdout
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Veeqo sellables',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running import script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing Veeqo sellables: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import Shopify Matrixify export to imported_table_shopify_products_dz
app.post('/api/import-shopify-matrixify', (req, res) => {
  console.log('[adminServer] Running import_shopify_matrixify.js');

  try {
    // Run the import script
    const importProcess = spawn('node', ['import_shopify_matrixify.js']);

    let stdout = '';
    let stderr = '';

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[import_shopify_matrixify.js] ${output.trim()}`);
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[import_shopify_matrixify.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] import_shopify_matrixify.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Shopify Matrixify export imported successfully to imported_table_shopify_products_dz',
          details: {
            stdout
          }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Shopify Matrixify export to imported_table_shopify_products_dz',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running import script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing Shopify Matrixify export: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile discs to Veeqo
app.post('/api/reconcile-d-to-veeqo', (req, res) => {
  console.log('[adminServer] Running reconcileDToVeeqo.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileDToVeeqo.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileDToVeeqo.js] ${output.trim()}`);

      // Try to extract summary from the output
      const summaryMatch = output.match(/Total records processed: (\d+)[\s\S]*?Successful updates: (\d+)[\s\S]*?Failed updates: (\d+)/);

      if (summaryMatch) {
        summary = {
          totalProcessed: parseInt(summaryMatch[1]),
          successCount: parseInt(summaryMatch[2]),
          failureCount: parseInt(summaryMatch[3])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileDToVeeqo.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileDToVeeqo.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Disc to Veeqo reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile discs to Veeqo',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling discs to Veeqo: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to update Veeqo quantities from RPRO
app.post('/api/update-veeqo-from-rpro', (req, res) => {
  console.log('[adminServer] Running updateVeeqoFromRpro.js');

  try {
    // Run the update script
    const updateProcess = spawn('node', ['updateVeeqoFromRpro.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    updateProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[updateVeeqoFromRpro.js] ${output.trim()}`);

      // Try to extract summary information from the output
      const totalProcessedMatch = output.match(/Total records processed: (\d+)/);
      const successCountMatch = output.match(/Successful updates: (\d+)/);
      const failureCountMatch = output.match(/Failed updates: (\d+)/);
      const completedMatch = output.match(/Veeqo update from RPRO completed/);

      if (totalProcessedMatch || successCountMatch || failureCountMatch || completedMatch) {
        if (!summary) summary = {};
        if (totalProcessedMatch) summary.totalProcessed = parseInt(totalProcessedMatch[1]);
        if (successCountMatch) summary.successCount = parseInt(successCountMatch[1]);
        if (failureCountMatch) summary.failureCount = parseInt(failureCountMatch[1]);
        if (completedMatch) summary.completed = true;
      }
    });

    updateProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[updateVeeqoFromRpro.js] ${output.trim()}`);
    });

    updateProcess.on('close', (code) => {
      console.log(`[adminServer] updateVeeqoFromRpro.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Veeqo quantities updated from RPRO successfully',
          details: {
            stdout,
            stderr
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to update Veeqo quantities from RPRO',
          stdout,
          stderr
        });
      }
    });

    updateProcess.on('error', (err) => {
      console.error('[adminServer] Error running updateVeeqoFromRpro script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });

  } catch (err) {
    console.error('[adminServer] Error in update-veeqo-from-rpro:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile OSL stats into t_inv_osl
app.post('/api/reconcile-osl-stats', (req, res) => {
  console.log('[adminServer] Running reconcileOslStats.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileOslStats.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileOslStats.js] ${output.trim()}`);

      // Try to extract summary from the output
      const totalBatchesMatch = output.match(/Total batches: (\d+)/);
      const totalUpdatedMatch = output.match(/Total records updated: (\d+)/);

      if (totalBatchesMatch && totalUpdatedMatch) {
        summary = {
          totalBatches: parseInt(totalBatchesMatch[1]),
          totalUpdated: parseInt(totalUpdatedMatch[1])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileOslStats.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileOslStats.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'OSL stats reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile OSL stats',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling OSL stats: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile SDAsin stats into t_inv_sdasin
app.post('/api/reconcile-sdasin-stats', (req, res) => {
  console.log('[adminServer] Running reconcileSdasinStats.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['reconcileSdasinStats.js']);

    let stdout = '';
    let stderr = '';
    let summary = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[reconcileSdasinStats.js] ${output.trim()}`);

      // Try to extract summary from the output
      const totalBatchesMatch = output.match(/Total batches: (\d+)/);
      const totalUpdatedMatch = output.match(/Total records updated: (\d+)/);

      if (totalBatchesMatch && totalUpdatedMatch) {
        summary = {
          totalBatches: parseInt(totalBatchesMatch[1]),
          totalUpdated: parseInt(totalUpdatedMatch[1])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[reconcileSdasinStats.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] reconcileSdasinStats.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'SDAsin stats reconciliation completed successfully',
          details: {
            stdout
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to reconcile SDAsin stats',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running reconcile script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling SDAsin stats: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile OSLs to Veeqo
app.post('/api/reconcile-osl-to-veeqo', async (req, res) => {
  console.log('[adminServer] Running reconcileOslToVeeqo.js');

  try {
    // Import the reconcileOslToVeeqo module
    const reconcileOslToVeeqo = (await import('./reconcileOslToVeeqo.js')).default;

    // Run the reconcile function
    const result = await reconcileOslToVeeqo();
    const { tasks, summary } = result;

    // Return the results
    res.json({
      success: true,
      message: `Successfully processed ${summary.totalProcessed} records (${summary.skippedRecords} skipped due to missing available_quantity) and enqueued ${summary.tasksEnqueued} tasks to update OSL quantities in Veeqo.`,
      count: tasks.length,
      tasks: tasks,
      summary: summary
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling OSLs to Veeqo: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to reconcile SDAsins to Veeqo
app.post('/api/reconcile-sdasin-to-veeqo', async (req, res) => {
  console.log('[adminServer] Reconciling SDAsins to Veeqo');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Query the v_reconcile_disc_to_veeqo view for records with quantity mismatches
    const { data: discrepancies, error: queryError } = await supabase
      .from('v_reconcile_disc_to_veeqo')
      .select('sdasin_id, fbm_sku, available_quantity, total_qty, issue')
      .eq('issue', "qtys don't match");

    if (queryError) {
      console.error(`[adminServer] Error querying v_reconcile_disc_to_veeqo: ${queryError.message}`);
      return res.status(500).json({
        success: false,
        error: queryError.message
      });
    }

    if (!discrepancies || discrepancies.length === 0) {
      return res.json({
        success: true,
        message: 'No SDAsin quantity discrepancies found in Veeqo.',
        count: 0,
        tasks: [],
        summary: {
          totalProcessed: 0,
          tasksEnqueued: 0
        }
      });
    }

    console.log(`[adminServer] Found ${discrepancies.length} SDAsin quantity discrepancies`);

    // Enqueue tasks for each discrepancy
    const now = new Date();

    const tasks = discrepancies.map(record => ({
      task_type: 'update_veeqo_sdasin_qty',
      payload: {
        id: record.sdasin_id,
        available_quantity: record.available_quantity
      },
      status: 'pending',
      scheduled_at: now.toISOString(), // Schedule immediately
      created_at: now.toISOString(),
      enqueued_by: `reconcile_sdasin_to_veeqo_${record.sdasin_id}`
    }));

    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasks)
      .select('id, payload');

    if (enqueueError) {
      console.error(`[adminServer] Error enqueueing tasks: ${enqueueError.message}`);
      return res.status(500).json({
        success: false,
        error: enqueueError.message
      });
    }

    console.log(`[adminServer] Successfully enqueued ${enqueuedTasks.length} tasks`);

    // Update imported_table_veeqo_sellables_export to make the process idempotent
    console.log(`[adminServer] Updating imported_table_veeqo_sellables_export total_qty for ${discrepancies.length} records`);

    let veeqoUpdateCount = 0;
    let veeqoUpdateErrors = [];

    for (const record of discrepancies) {
      try {
        const { error: veeqoUpdateError } = await supabase
          .from('imported_table_veeqo_sellables_export')
          .update({
            total_qty: record.available_quantity
          })
          .eq('sku_code', record.fbm_sku);

        if (veeqoUpdateError) {
          console.error(`[adminServer] Error updating imported_table_veeqo_sellables_export for SKU ${record.fbm_sku}: ${veeqoUpdateError.message}`);
          veeqoUpdateErrors.push(`SKU ${record.fbm_sku}: ${veeqoUpdateError.message}`);
        } else {
          veeqoUpdateCount++;
        }
      } catch (err) {
        console.error(`[adminServer] Exception updating imported_table_veeqo_sellables_export for SKU ${record.fbm_sku}: ${err.message}`);
        veeqoUpdateErrors.push(`SKU ${record.fbm_sku}: ${err.message}`);
      }
    }

    console.log(`[adminServer] Updated ${veeqoUpdateCount} records in imported_table_veeqo_sellables_export`);
    if (veeqoUpdateErrors.length > 0) {
      console.log(`[adminServer] ${veeqoUpdateErrors.length} errors updating imported_table_veeqo_sellables_export:`, veeqoUpdateErrors);
    }

    res.json({
      success: true,
      message: `Successfully enqueued ${enqueuedTasks.length} tasks to update SDAsin quantities in Veeqo. Updated ${veeqoUpdateCount} local Veeqo export records to make process idempotent.`,
      count: enqueuedTasks.length,
      tasks: enqueuedTasks.map(task => ({
        id: task.id,
        sdasin_id: task.payload.id
      })),
      summary: {
        totalProcessed: discrepancies.length,
        tasksEnqueued: enqueuedTasks.length,
        veeqoExportUpdated: veeqoUpdateCount,
        veeqoExportErrors: veeqoUpdateErrors.length
      }
    });
  } catch (err) {
    console.error(`[adminServer] Exception reconciling SDAsins to Veeqo: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to refresh RPRO to Veeqo reconciliation data
app.post('/api/reconcile-rpro-to-veeqo', (req, res) => {
  console.log('[adminServer] Running refreshReconcileData.js');

  try {
    // Run the reconcile script
    const reconcileProcess = spawn('node', ['refreshReconcileData.js']);

    let stdout = '';
    let stderr = '';
    let stats = null;

    reconcileProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[refreshReconcileData.js] ${output.trim()}`);

      // Try to extract statistics from the output
      const totalMatch = output.match(/Total records: (\d+)/);
      const discrepancyMatch = output.match(/Records with discrepancies: (\d+)/);
      const rproMoreMatch = output.match(/Records where RPRO has more: (\d+)/);
      const veeqoMoreMatch = output.match(/Records where Veeqo has more: (\d+)/);
      const matchingMatch = output.match(/Records with matching quantities: (\d+)/);

      if (totalMatch && discrepancyMatch && rproMoreMatch && veeqoMoreMatch && matchingMatch) {
        stats = {
          totalCount: parseInt(totalMatch[1]),
          discrepancyCount: parseInt(discrepancyMatch[1]),
          rproMoreCount: parseInt(rproMoreMatch[1]),
          veeqoMoreCount: parseInt(veeqoMoreMatch[1]),
          matchingCount: parseInt(matchingMatch[1])
        };
      }
    });

    reconcileProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[refreshReconcileData.js] ${output.trim()}`);
    });

    reconcileProcess.on('close', (code) => {
      console.log(`[adminServer] refreshReconcileData.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'RPRO to Veeqo reconciliation data refreshed successfully',
          details: {
            stdout,
            stderr
          },
          stats: stats
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to refresh RPRO to Veeqo reconciliation data',
          stdout,
          stderr
        });
      }
    });

    reconcileProcess.on('error', (err) => {
      console.error('[adminServer] Error running refreshReconcileData script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });

  } catch (err) {
    console.error('[adminServer] Error in reconcile-rpro-to-veeqo:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import RPRO data
app.post('/api/import-rpro-data', (req, res) => {
  console.log('[adminServer] Running batchImport.js for RPRO data');

  try {
    // Run the import script with default parameters
    const dbfFilePath = process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
    const targetTable = process.env.TARGET_TABLE || 'imported_table_rpro';
    const truncateBeforeImport = process.env.TRUNCATE_BEFORE_IMPORT === 'true' || true;

    const importProcess = spawn('node', ['batchImport.js', dbfFilePath, targetTable, truncateBeforeImport.toString()]);

    let stdout = '';
    let stderr = '';
    let summary = null;

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[batchImport.js] ${output.trim()}`);

      // Try to extract summary information from the output
      const totalRecordsMatch = output.match(/Total records in DBF file: (\d+)/);
      const totalBatchesMatch = output.match(/Total batches: (\d+)/);
      const completedMatch = output.match(/Import completed successfully/);
      const durationMatch = output.match(/Import duration: ([\d.]+) seconds/);

      if (totalRecordsMatch || totalBatchesMatch || completedMatch || durationMatch) {
        if (!summary) summary = {};
        if (totalRecordsMatch) summary.totalRecords = parseInt(totalRecordsMatch[1]);
        if (totalBatchesMatch) summary.totalBatches = parseInt(totalBatchesMatch[1]);
        if (durationMatch) summary.duration = `${durationMatch[1]} seconds`;
        if (completedMatch) summary.completed = true;
      }
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[batchImport.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] batchImport.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'RPRO data import completed successfully',
          details: {
            stdout,
            stderr
          },
          summary: summary
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import RPRO data',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running batchImport script:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });

  } catch (err) {
    console.error('[adminServer] Error in import-rpro-data:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to import Amazon Active Listings Report
app.post('/api/import-amazon-active-listings', async (req, res) => {
  console.log('[adminServer] Running importAmazonActiveListings.js');

  try {
    // Import the function from the module
    const { importActiveListingsReport } = await import('./importAmazonActiveListings.js');

    // Run the import function
    const result = await importActiveListingsReport();

    console.log('[adminServer] Amazon Active Listings import completed successfully');
    res.json({
      success: true,
      message: 'Amazon Active Listings Report import completed successfully',
      summary: result
    });

  } catch (err) {
    console.error('[adminServer] Error in import-amazon-active-listings:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to enqueue generate_osl_fields tasks for OSLs with null g_code
app.post('/api/enqueue-generate-osl-fields', async (req, res) => {
  console.log('[adminServer] Enqueueing generate_osl_fields tasks for OSLs with null g_code');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Get the batch size from the request body
    const batchSize = req.body.batchSize || 100;
    console.log(`[adminServer] Batch size: ${batchSize}`);

    // Call the function to enqueue tasks
    const { data, error } = await supabase.rpc(
      'fn_enqueue_generate_osl_fields_for_null_g_code',
      { batch_size: batchSize }
    );

    if (error) {
      console.error(`[adminServer] Error calling fn_enqueue_generate_osl_fields_for_null_g_code: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Return the results
    res.json({
      success: true,
      message: `Successfully enqueued ${data.length} tasks for OSLs with null g_code.`,
      count: data.length,
      tasks: data
    });
  } catch (err) {
    console.error(`[adminServer] Exception enqueueing generate_osl_fields tasks: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to enqueue sold discs Shopify tasks
app.post('/api/enqueue-sold-discs-shopify-tasks', async (req, res) => {
  console.log('[adminServer] Enqueueing sold discs Shopify tasks');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Query the reconciliation view to find sold discs still showing on Shopify
    const { data: soldDiscs, error: queryError } = await supabase
      .from('v_reconcile_d_to_shopify')
      .select('disc_id, shopify_product_id, shopify_variant_id, shopify_variant_inventory_item_id')
      .eq('issue_description', 'Sold disc still showing up on Shopify.');

    if (queryError) {
      console.error(`[adminServer] Error querying sold discs: ${queryError.message}`);
      return res.status(500).json({
        success: false,
        error: queryError.message
      });
    }

    if (!soldDiscs || soldDiscs.length === 0) {
      return res.json({
        success: true,
        message: 'No sold discs found that are still showing on Shopify.',
        count: 0,
        tasks: []
      });
    }

    console.log(`[adminServer] Found ${soldDiscs.length} sold discs still showing on Shopify`);

    // Enqueue tasks for each sold disc
    const now = new Date();

    const tasks = soldDiscs.map(disc => ({
      task_type: 'reconcile_clear_count_from_shopify_for_sold_disc',
      payload: {
        disc_id: disc.disc_id
      },
      status: 'pending',
      scheduled_at: now.toISOString(), // Schedule immediately
      created_at: now.toISOString(),
      enqueued_by: `enqueue_sold_discs_shopify_tasks_${disc.disc_id}`
    }));

    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasks)
      .select('id, payload');

    if (enqueueError) {
      console.error(`[adminServer] Error enqueueing tasks: ${enqueueError.message}`);
      return res.status(500).json({
        success: false,
        error: enqueueError.message
      });
    }

    console.log(`[adminServer] Successfully enqueued ${enqueuedTasks.length} tasks`);

    res.json({
      success: true,
      message: `Successfully enqueued ${enqueuedTasks.length} tasks to clear Shopify inventory for sold discs.`,
      count: enqueuedTasks.length,
      tasks: enqueuedTasks.map(task => ({
        id: task.id,
        disc_id: task.payload.disc_id
      }))
    });

  } catch (err) {
    console.error(`[adminServer] Exception enqueueing sold discs Shopify tasks: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Start server on fixed port 3001 only
function startServer(port) {
  const localIP = getLocalIPAddress();
  const server = app.listen(port, '0.0.0.0')
    .on('listening', () => {
      console.log(`[adminServer] Server running on port ${port} (LAN accessible)`);
      console.log(`[adminServer] Local access: http://localhost:${port}/admin.html`);
      console.log(`[adminServer] LAN access: http://${localIP}:${port}/admin.html`);
      console.log(`[adminServer] ToDo interface (LAN): http://${localIP}:${port}/ToDo.html`);
    })
    .on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`[adminServer] ERROR: Port ${port} is already in use!`);
        console.error(`[adminServer] Please stop the process using port ${port} and restart the admin server.`);
        console.error(`[adminServer] You can find what's using the port with: netstat -ano | findstr :${port}`);
        process.exit(1);
      } else {
        console.error('[adminServer] Error starting server:', err);
        process.exit(1);
      }
    });
}

// API endpoint for importing discs from Google Sheets
app.post('/api/import-discs-from-sheets', async (req, res) => {
  console.log('[adminServer] Import discs from Google Sheets request received');

  try {
    const { googleSheetsUrl, validateOnly } = req.body;

    if (!googleSheetsUrl) {
      return res.status(400).json({
        success: false,
        error: 'Google Sheets URL is required'
      });
    }

    console.log(`[adminServer] Processing Google Sheets import: ${googleSheetsUrl}, validateOnly: ${validateOnly}`);

    const result = await importDiscsFromGoogleSheets(googleSheetsUrl, validateOnly);

    res.json(result);

  } catch (err) {
    console.error(`[adminServer] Exception importing discs from Google Sheets: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// API endpoint for Google Sheets to lookup g_code by mps_id
app.get('/api/lookup-gcode/:mpsId', async (req, res) => {
  console.log('[adminServer] G-code lookup request received');

  try {
    const { mpsId } = req.params;

    if (!mpsId || isNaN(parseInt(mpsId))) {
      return res.status(400).json({
        success: false,
        error: 'Valid mps_id is required'
      });
    }

    console.log(`[adminServer] Looking up g_code for mps_id: ${mpsId}`);

    const { data, error } = await supabase
      .from('t_mps')
      .select('g_code')
      .eq('id', parseInt(mpsId))
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return res.json({
          success: true,
          gCode: null,
          message: 'MPS ID not found'
        });
      }
      throw error;
    }

    res.json({
      success: true,
      gCode: data.g_code,
      mpsId: parseInt(mpsId)
    });

  } catch (err) {
    console.error(`[adminServer] Exception looking up g_code: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Register Informed API endpoints
registerInformedApiEndpoints(app);

// Discraft API endpoints
app.post('/api/discraft/import', (req, res) => {
  console.log('[adminServer] Running Discraft import');

  try {
    const importProcess = spawn('node', ['fullDiscraftImport.js']);
    let stdout = '';
    let stderr = '';
    let importResults = {};

    importProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[fullDiscraftImport.js] ${output.trim()}`);

      const resultsMatch = output.match(/Successfully imported (\d+) records/);
      if (resultsMatch) {
        importResults.totalProducts = parseInt(resultsMatch[1]);
      }

      const batchMatch = output.match(/Import batch ID: ([a-f0-9-]+)/);
      if (batchMatch) {
        importResults.batchId = batchMatch[1];
      }
    });

    importProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[fullDiscraftImport.js] ${output.trim()}`);
    });

    importProcess.on('close', (code) => {
      console.log(`[adminServer] fullDiscraftImport.js exited with code ${code}`);

      if (code === 0) {
        res.json({
          success: true,
          message: 'Discraft import completed successfully',
          totalProducts: importResults.totalProducts,
          batchId: importResults.batchId,
          details: { stdout }
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to import Discraft catalog',
          stdout,
          stderr
        });
      }
    });

    importProcess.on('error', (err) => {
      console.error('[adminServer] Error running Discraft import:', err);
      res.status(500).json({
        success: false,
        error: err.message
      });
    });
  } catch (err) {
    console.error(`[adminServer] Exception running Discraft import: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/discraft/status', async (req, res) => {
  console.log('[adminServer] Getting Discraft status');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    const { count: totalProducts, error: totalError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('[adminServer] Error getting total products:', totalError);
      return res.status(500).json({ error: totalError.message });
    }

    const { count: availableProducts, error: availableError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('is_currently_available', true);

    if (availableError) {
      console.error('[adminServer] Error getting available products:', availableError);
      return res.status(500).json({ error: availableError.message });
    }

    const { count: outOfStockProducts, error: outOfStockError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('is_currently_available', false);

    if (outOfStockError) {
      console.error('[adminServer] Error getting out of stock products:', outOfStockError);
      return res.status(500).json({ error: outOfStockError.message });
    }

    const { data: mappingKeys, error: mappingError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_mapping_key');

    if (mappingError) {
      console.error('[adminServer] Error getting mapping keys:', mappingError);
      return res.status(500).json({ error: mappingError.message });
    }

    const uniqueKeys = new Set(mappingKeys.map(row => row.excel_mapping_key)).size;

    const { data: lastImportData, error: lastImportError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('created_at')
      .order('created_at', { ascending: false })
      .limit(1);

    let lastImport = 'Never';
    if (!lastImportError && lastImportData && lastImportData.length > 0) {
      lastImport = new Date(lastImportData[0].created_at).toLocaleString();
    }

    res.json({
      success: true,
      totalProducts: totalProducts || 0,
      availableProducts: availableProducts || 0,
      outOfStockProducts: outOfStockProducts || 0,
      uniqueKeys: uniqueKeys || 0,
      lastImport
    });

  } catch (err) {
    console.error('[adminServer] Exception getting Discraft status:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/calculate-mps', async (req, res) => {
  console.log('[adminServer] Calculating Discraft MPS IDs');
  try {
    // Get all Discraft products in chunks for resetting
    console.log('[adminServer] Resetting existing MPS IDs...');
    let allProducts = [];
    let offset = 0;
    const chunkSize = 1000;

    while (true) {
      const { data: productChunk, error: chunkError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('id')
        .range(offset, offset + chunkSize - 1);

      if (chunkError) {
        console.error('[adminServer] Error getting products chunk:', chunkError);
        return res.status(500).json({ error: chunkError.message });
      }

      if (!productChunk || productChunk.length === 0) break;

      allProducts = allProducts.concat(productChunk);

      if (productChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    // Reset all calculated_mps_id to NULL (with WHERE clause)
    for (const product of allProducts) {
      await supabase
        .from('it_discraft_order_sheet_lines')
        .update({ calculated_mps_id: null })
        .eq('id', product.id);
    }

    // Get all Discraft products in chunks
    console.log('[adminServer] Getting Discraft products...');
    let allDiscraftProducts = [];
    offset = 0;

    while (true) {
      const { data: productChunk, error: discraftError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('id, plastic_name, mold_name, stamp_name')
        .range(offset, offset + chunkSize - 1);

      if (discraftError) {
        console.error('[adminServer] Error getting Discraft products:', discraftError);
        return res.status(500).json({ error: discraftError.message });
      }

      if (!productChunk || productChunk.length === 0) break;

      allDiscraftProducts = allDiscraftProducts.concat(productChunk);

      if (productChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    // Get all Discraft MPS records
    console.log('[adminServer] Getting Discraft MPS records...');
    const { data: mpsRecords, error: mpsError } = await supabase
      .from('t_mps')
      .select(`id, t_plastics!inner(plastic, brand_id), t_molds!inner(mold, brand_id), t_stamps!inner(stamp)`)
      .eq('active', true)
      .eq('t_plastics.brand_id', 6)
      .eq('t_molds.brand_id', 6);

    if (mpsError) {
      console.error('[adminServer] Error getting MPS records:', mpsError);
      return res.status(500).json({ error: mpsError.message });
    }

    // Create lookup map
    const mpsMap = new Map();
    mpsRecords.forEach(mps => {
      const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
      mpsMap.set(key, mps.id);
    });

    // Match products to MPS records
    console.log('[adminServer] Matching products to MPS records...');
    let matchedCount = 0;

    for (const product of allDiscraftProducts) {
      const key = `${product.plastic_name.trim()}|${product.mold_name.trim()}|${product.stamp_name.trim()}`;
      const mpsId = mpsMap.get(key);

      if (mpsId) {
        await supabase
          .from('it_discraft_order_sheet_lines')
          .update({ calculated_mps_id: mpsId })
          .eq('id', product.id);
        matchedCount++;
      }
    }

    const failedCount = allDiscraftProducts.length - matchedCount;
    const successRate = ((matchedCount / allDiscraftProducts.length) * 100).toFixed(2);

    res.json({
      success: true,
      totalProducts: allDiscraftProducts.length,
      calculatedCount: matchedCount,
      failedCount: failedCount,
      successRate: parseFloat(successRate)
    });
  } catch (err) {
    console.error('[adminServer] Exception calculating MPS IDs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/analyze-matching', async (req, res) => {
  console.log('[adminServer] Analyzing Discraft matching');
  try {
    // Get total counts using count queries instead of loading all data
    const { count: totalProducts, error: totalError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (totalError) throw totalError;

    const { count: withMpsId, error: withMpsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .not('calculated_mps_id', 'is', null);

    if (withMpsError) throw withMpsError;

    const withoutMpsId = totalProducts - withMpsId;
    const mpsSuccessRate = ((withMpsId / totalProducts) * 100).toFixed(1);
    // Get unmatched products in chunks
    let allUnmatched = [];
    let offset = 0;
    const chunkSize = 1000;

    while (true) {
      const { data: unmatchedChunk, error: unmatchedError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('plastic_name, mold_name, stamp_name')
        .is('calculated_mps_id', null)
        .range(offset, offset + chunkSize - 1);

      if (unmatchedError) throw unmatchedError;

      if (!unmatchedChunk || unmatchedChunk.length === 0) break;

      allUnmatched = allUnmatched.concat(unmatchedChunk);

      if (unmatchedChunk.length < chunkSize) break;
      offset += chunkSize;
    }

    const unmatchedGroups = {};
    allUnmatched.forEach(row => {
      const key = `${row.plastic_name}|${row.mold_name}|${row.stamp_name}`;
      unmatchedGroups[key] = (unmatchedGroups[key] || 0) + 1;
    });
    const topUnmatched = Object.entries(unmatchedGroups)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([key, count]) => {
        const [plastic_name, mold_name, stamp_name] = key.split('|');
        return { plastic_name, mold_name, stamp_name, count };
      });
    res.json({ success: true, withMpsId, withoutMpsId, mpsSuccessRate, topUnmatched });
  } catch (err) {
    console.error('[adminServer] Exception analyzing matching:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/find-osl-matches', async (req, res) => {
  console.log('[adminServer] Finding OSL to Discraft matches');
  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }
    const { data: matches, error: matchError } = await supabase.rpc('find_discraft_osl_matches');
    if (matchError) {
      console.error('[adminServer] Error finding OSL matches:', matchError);
      return res.status(500).json({ error: matchError.message });
    }
    const perfectMatches = matches.filter(m =>
      m.weight_status === 'COMPATIBLE' &&
      (m.color_status === 'ANY_COLOR_OK' || m.color_status === 'VARIES_OK' || m.color_status === 'COLOR_MATCH')
    ).length;
    const weightConflicts = matches.filter(m => m.weight_status === 'INCOMPATIBLE').length;
    const colorConflicts = matches.filter(m => m.color_status === 'COLOR_MISMATCH').length;
    const uniqueOslsWithMatches = new Set(matches.map(m => m.osl_id)).size;
    const oslMatchCounts = {};
    matches.forEach(m => {
      oslMatchCounts[m.osl_id] = (oslMatchCounts[m.osl_id] || 0) + 1;
    });
    const multipleMatches = Object.values(oslMatchCounts).filter(count => count > 1).length;
    const { count: totalDiscraftOsls, error: countError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', 9);
    if (countError) throw countError;
    const noMatches = (totalDiscraftOsls || 0) - uniqueOslsWithMatches;
    res.json({
      success: true, perfectMatches, weightConflicts, colorConflicts, multipleMatches, noMatches,
      totalOsls: totalDiscraftOsls || 0, matchDetails: matches.slice(0, 20)
    });
  } catch (err) {
    console.error('[adminServer] Exception finding OSL matches:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-unmatched', async (req, res) => {
  console.log('[adminServer] Getting unmatched products for review');
  try {
    const { data: unmatchedProducts, error: productsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, vendor_description, min_weight, max_weight, is_currently_available')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(50);
    if (productsError) throw productsError;

    // Get matched MPS IDs first
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);

    if (matchedError) throw matchedError;

    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);

    const { data: unmatchedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp))`)
      .eq('vendor_id', 9)
      .not('mps_id', 'in', `(${matchedIds.length > 0 ? matchedIds.join(',') : '0'})`)
      .limit(50);
    if (oslsError) throw oslsError;
    res.json({
      success: true,
      unmatchedProducts: unmatchedProducts || [],
      unmatchedOsls: unmatchedOsls || []
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched products:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-unmatched-discraft-products', async (req, res) => {
  console.log('[adminServer] Getting Discraft products that don\'t match our OSLs');
  try {
    // Get all matched MPS IDs from Discraft
    const { data: matchedMpsIds, error: matchedError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id')
      .not('calculated_mps_id', 'is', null);

    if (matchedError) throw matchedError;

    const matchedIds = matchedMpsIds.map(row => row.calculated_mps_id);

    // Get Discraft products that don't have matches
    const { data: unmatchedDiscraftProducts, error: discraftError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, vendor_description, min_weight, max_weight, is_currently_available, is_orderable')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(100);

    if (discraftError) throw discraftError;

    // Get total count
    const { count: totalCount, error: countError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .is('calculated_mps_id', null)
      .eq('is_orderable', true);

    if (countError) throw countError;

    res.json({
      success: true,
      unmatchedDiscraftProducts: unmatchedDiscraftProducts || [],
      totalUnmatchedDiscraftProducts: totalCount || 0
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched Discraft products:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

app.get('/api/discraft/review-unmatched-osls', async (req, res) => {
  console.log('[adminServer] Getting unmatched Discraft OSLs');
  try {
    const showInactive = req.query.showInactive === 'true';

    // Get all Discraft vendor products with their MPS ID and weight ranges
    const { data: discraftProducts, error: discraftError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('calculated_mps_id, min_weight, max_weight')
      .not('calculated_mps_id', 'is', null);

    if (discraftError) throw discraftError;

    // Get all OSLs for vendor_id 9 (Discraft)
    let oslQuery = supabase
      .from('t_order_sheet_lines')
      .select(`id, mps_id, min_weight, max_weight, color_id, t_colors(color), t_mps!inner(t_plastics!inner(plastic), t_molds!inner(mold), t_stamps!inner(stamp), active)`)
      .eq('vendor_id', 9);

    // Filter by MPS active status if not showing inactive
    if (!showInactive) {
      oslQuery = oslQuery.eq('t_mps.active', true);
    }

    const { data: allOsls, error: oslsError } = await oslQuery.limit(2000);
    if (oslsError) throw oslsError;

    // Filter OSLs to find those without matching Discraft products
    const unmatchedOsls = allOsls.filter(osl => {
      // Check if there's a Discraft product with matching MPS ID and overlapping weight range
      const hasMatch = discraftProducts.some(product => {
        return product.calculated_mps_id === osl.mps_id &&
               product.min_weight <= osl.max_weight &&
               product.max_weight >= osl.min_weight;
      });
      return !hasMatch;
    });

    // Limit to first 1000 unmatched OSLs
    const limitedUnmatchedOsls = unmatchedOsls.slice(0, 1000);

    // Add color information to unmatched OSLs
    const oslsWithColors = limitedUnmatchedOsls.map(osl => ({
      ...osl,
      color_name: osl.t_colors ? osl.t_colors.color : null
    }));

    res.json({
      success: true,
      unmatchedOsls: oslsWithColors,
      totalUnmatchedOsls: unmatchedOsls.length,
      showInactive: showInactive
    });
  } catch (err) {
    console.error('[adminServer] Exception getting unmatched OSLs:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to mark MPS as inactive for Discraft
app.post('/api/discraft/mark-mps-inactive', async (req, res) => {
  console.log('[adminServer] Marking Discraft MPS as inactive');
  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: false })
      .eq('id', mpsId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as inactive successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as inactive:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to mark MPS as active for Discraft
app.post('/api/discraft/mark-mps-active', async (req, res) => {
  console.log('[adminServer] Marking Discraft MPS as active');
  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    // Update the MPS record to set active = true
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: true })
      .eq('id', mpsId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as active`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as active successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as active:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// API endpoint to test Supabase connection
app.get('/api/test-connection', async (req, res) => {
  console.log('[adminServer] Testing Supabase connection');

  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized'
    });
  }

  try {
    // Simple query to test connection
    const { data, error } = await supabase
      .from('v_stats_by_osl_discraft')
      .select('count(*)', { count: 'exact', head: true });

    if (error) {
      return res.status(500).json({
        success: false,
        error: 'Database query failed',
        details: error.message
      });
    }

    res.json({
      success: true,
      message: 'Connection successful',
      recordCount: data || 'Unknown'
    });

  } catch (err) {
    console.error('[adminServer] Connection test error:', err);
    res.status(500).json({
      success: false,
      error: 'Connection test failed',
      details: err.message
    });
  }
});

// API endpoint to export Discraft order data back to Excel (for automation)
app.post('/api/discraft/export', async (req, res) => {
  console.log('[adminServer] Exporting Discraft order data with header');

  const { includeHeader = false, filename, orderData, enhancedMpsExport = false } = req.body;

  // Check Supabase connection first
  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized',
      details: 'Database connection not available'
    });
  }

  try {
    // Define file paths
    const inputFile = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
    const now = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]; // YYYY-MM-DD-HH-MM-SS format
    const outputFilename = filename || `discraftstock_${now}.xlsx`;
    const outputFile = path.join(__dirname, 'data', 'external data', outputFilename);

    // Check if input file exists
    if (!fs.existsSync(inputFile)) {
      return res.status(400).json({
        success: false,
        error: 'Input file not found',
        details: `Expected file: ${inputFile}`
      });
    }

    // Use provided order data if available, otherwise query the view
    let finalOrderData;
    if (orderData && Array.isArray(orderData)) {
      console.log(`[adminServer] Using provided order data: ${orderData.length} records`);
      // Transform the provided data to match the expected format
      // Include ALL records, not just ones with orders > 0, so we can put '0' in empty cells
      finalOrderData = orderData.map(item => ({
        excel_mapping_key: item.excel_mapping_key,
        excel_column: item.excel_column,
        excel_row_hint: item.excel_row_hint,
        order: item.order || 0,  // Default to 0 if no order
        calculated_mps_id: item.calculated_mps_id,  // Include MPS ID for enhanced export
        id: item.id  // Include order sheet line ID for enhanced export
      }));
    } else {
      console.log(`[adminServer] No order data provided, querying database...`);

      // Get ALL orderable products from the base table using pagination to avoid limits
      let allOrderableData = [];
      let from = 0;
      const pageSize = 1000;

      while (true) {
        const { data: batch, error: orderableError } = await supabase
          .from('it_discraft_order_sheet_lines')
          .select('id, excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
          .eq('is_orderable', true)
          .not('excel_mapping_key', 'is', null)  // Must have Excel mapping
          .range(from, from + pageSize - 1)
          .order('excel_mapping_key');

        if (orderableError) {
          throw new Error(`Failed to query orderable data: ${orderableError.message}`);
        }

        if (batch.length === 0) break;

        allOrderableData = allOrderableData.concat(batch);
        from += pageSize;

        if (batch.length < pageSize) break; // Last page
      }

      // Get order quantities from the view for matched products only using pagination
      let viewOrderData = [];
      from = 0;

      while (true) {
        const { data: batch, error: viewError } = await supabase
          .from('v_stats_by_osl_discraft')
          .select('excel_mapping_key, "order"')
          .not('excel_mapping_key', 'is', null)
          .range(from, from + pageSize - 1);

        if (viewError) {
          throw new Error(`Failed to query view order data: ${viewError.message}`);
        }

        if (batch.length === 0) break;

        viewOrderData = viewOrderData.concat(batch);
        from += pageSize;

        if (batch.length < pageSize) break; // Last page
      }

      // Create a map of order quantities by excel_mapping_key
      const orderMap = {};
      viewOrderData.forEach(item => {
        orderMap[item.excel_mapping_key] = item.order || 0;
      });

      // Combine all orderable products with their order quantities (defaulting to 0)
      finalOrderData = allOrderableData.map(item => ({
        excel_mapping_key: item.excel_mapping_key,
        excel_column: item.excel_column,
        excel_row_hint: item.excel_row_hint,
        order: orderMap[item.excel_mapping_key] || 0,  // Default to 0 if no order or not matched
        calculated_mps_id: item.calculated_mps_id,  // Include MPS ID for enhanced export
        id: item.id  // Include order sheet line ID for enhanced export
      }));
    }

    console.log(`[adminServer] Processing ${finalOrderData.length} records (including 0 quantities for troubleshooting)`);

    // Read the original Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(inputFile);
    const worksheet = workbook.getWorksheet(1);

    // Add order form header if requested
    if (includeHeader) {
      // Add the order form header to existing empty space without inserting rows
      // This preserves all Excel formatting and column mappings
      addOrderFormHeaderToExistingSpace(worksheet);
    }

    // Create a map of order data by unique cell address (row + column)
    // This prevents overwrites when multiple weight ranges exist for the same row
    const orderMapByCell = {};
    finalOrderData.forEach(row => {
      if (row.excel_row_hint && row.excel_column) {
        // Use row + column as unique key to prevent overwrites
        const cellKey = `${row.excel_column}${row.excel_row_hint}`;
        orderMapByCell[cellKey] = {
          order: row.order,
          excel_column: row.excel_column,
          excel_row_hint: row.excel_row_hint,
          excel_mapping_key: row.excel_mapping_key,
          calculated_mps_id: row.calculated_mps_id,
          id: row.id
        };
      }
    });

    let recordsProcessed = 0;
    let orderQuantitiesUpdated = 0;

    // Process order quantities using unique cell addresses
    for (const [cellKey, orderInfo] of Object.entries(orderMapByCell)) {
      recordsProcessed++;

      let columnLetter = orderInfo.excel_column;
      if (columnLetter === 'ASSORTED') {
        columnLetter = 'L';
      }

      const cellAddress = columnLetter + orderInfo.excel_row_hint;
      const cell = worksheet.getCell(cellAddress);
      const originalStyle = { ...cell.style };

      // For enhanced MPS export, use order sheet line ID in order cells and put MPS ID in column AC
      if (enhancedMpsExport) {
        const orderValue = orderInfo.id || 'NO_ID';  // Use order sheet line ID
        cell.value = orderValue;
        cell.style = originalStyle;

        // Also put MPS ID in column AC
        const acCellAddress = 'AC' + orderInfo.excel_row_hint;
        const acCell = worksheet.getCell(acCellAddress);
        const acOriginalStyle = { ...acCell.style };
        acCell.value = orderInfo.calculated_mps_id || 'NO_MPS';
        acCell.style = acOriginalStyle;

        console.log(`[adminServer] ✅ Enhanced MPS: Updated cell ${cellAddress} with order sheet line ID: ${orderValue}, AC${orderInfo.excel_row_hint} with MPS ID: ${orderInfo.calculated_mps_id || 'NO_MPS'}`);
      } else {
        const orderValue = orderInfo.order || 0;
        cell.value = orderValue;
        cell.style = originalStyle;

        console.log(`[adminServer] ✅ Updated cell ${cellAddress} (${orderInfo.excel_mapping_key}) with order quantity: ${orderValue}`);
      }

      orderQuantitiesUpdated++;
    }

    // Write the updated file
    await workbook.xlsx.writeFile(outputFile);

    console.log(`[adminServer] Export completed: ${outputFile}`);

    res.json({
      success: true,
      filename: outputFilename,
      filePath: outputFile,
      totalRecords: orderQuantitiesUpdated,
      headerIncluded: includeHeader
    });

  } catch (err) {
    console.error('[adminServer] Exception exporting Discraft orders:', err);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Helper function to add order form header to existing empty space
// This preserves all Excel formatting and column mappings by not inserting rows
function addOrderFormHeaderToExistingSpace(worksheet) {
  // Find empty space at the end of the sheet for the header
  // Look for the last row with data, then add header below it
  let lastRowWithData = 1;

  // Scan through the worksheet to find the last row with actual data
  worksheet.eachRow((row, rowNumber) => {
    // Check if this row has any non-empty cells
    let hasData = false;
    row.eachCell((cell) => {
      if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
        hasData = true;
      }
    });
    if (hasData) {
      lastRowWithData = rowNumber;
    }
  });

  // Start the header 3 rows after the last data row to give some space
  const headerStartRow = lastRowWithData + 3;

  console.log(`[adminServer] Adding order form header starting at row ${headerStartRow} (last data row: ${lastRowWithData})`);

  // Add the order form header starting at the calculated row
  const r = headerStartRow; // Base row for header

  // Row 1: STOCK ORDER FORM
  worksheet.getCell(`A${r}`).value = 'STOCK ORDER FORM';
  worksheet.getCell(`A${r}`).font = { bold: true, size: 14 };

  // Row 2: Phone and Fax
  worksheet.getCell(`A${r+1}`).value = 'Phone:************';
  worksheet.getCell(`Y${r+1}`).value = 'fax:************';

  // Row 3: Website and Email
  worksheet.getCell(`A${r+2}`).value = 'website: www.discraft.com';
  worksheet.getCell(`E${r+2}`).value = '51000 Grand River Ave, Wixom, Michigan, 48393';
  worksheet.getCell(`Y${r+2}`).value = 'Email: <EMAIL>';

  // Row 4: Billing and Shipping Address headers
  worksheet.getCell(`A${r+3}`).value = 'Billing Address';
  worksheet.getCell(`A${r+3}`).font = { bold: true, color: { argb: 'FFFF0000' } }; // Red
  worksheet.getCell(`N${r+3}`).value = 'Shipping Address';
  worksheet.getCell(`N${r+3}`).font = { bold: true, color: { argb: 'FFFF0000' } }; // Red
  worksheet.getCell(`Y${r+3}`).value = 'updated: 06/10/25';

  // Instead of adding to empty space, let's find the existing header structure and fill in the data
  // Look for existing "Name" cells and fill in the data next to them

  // Scan the worksheet to find the existing order form structure
  let nameRowFound = false;
  let addressRowFound = false;
  let cityRowFound = false;
  let phoneRowFound = false;
  let emailRowFound = false;

  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell, colNumber) => {
      const cellValue = cell.value ? cell.value.toString().toLowerCase() : '';

      // Look for "Name" field and fill in company name
      if (cellValue === 'name' && !nameRowFound) {
        // Fill in the cell next to "Name" with company name
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = 'DZ Discs';
          nameRowFound = true;
          console.log(`[adminServer] Filled Name field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }

      // Look for "address" field and fill in address
      if (cellValue === 'address' && !addressRowFound) {
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = '811 E 23rd St Suite E';
          addressRowFound = true;
          console.log(`[adminServer] Filled Address field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }

      // Look for "city" field and fill in city, state, zip
      if (cellValue === 'city' && !cityRowFound) {
        const cityCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!cityCell.value || cityCell.value.toString().trim() === '') {
          cityCell.value = 'Lawrence';

          // Look for state and zip in the same row
          row.eachCell((stateCell, stateColNumber) => {
            if (stateCell.value && stateCell.value.toString().toLowerCase() === 'state') {
              const stateValueCell = worksheet.getCell(rowNumber, stateColNumber + 1);
              if (!stateValueCell.value || stateValueCell.value.toString().trim() === '') {
                stateValueCell.value = 'KS';
              }
            }
            if (stateCell.value && stateCell.value.toString().toLowerCase() === 'zip') {
              const zipValueCell = worksheet.getCell(rowNumber, stateColNumber + 1);
              if (!zipValueCell.value || zipValueCell.value.toString().trim() === '') {
                zipValueCell.value = '66046';
              }
            }
          });

          cityRowFound = true;
          console.log(`[adminServer] Filled City/State/Zip fields at row ${rowNumber}`);
        }
      }

      // Look for "phone" field and fill in phone
      if (cellValue === 'phone' && !phoneRowFound) {
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = '************';
          phoneRowFound = true;
          console.log(`[adminServer] Filled Phone field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }

      // Look for "email address" field and fill in email
      if (cellValue.includes('email') && cellValue.includes('address') && !emailRowFound) {
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = '<EMAIL>';
          emailRowFound = true;
          console.log(`[adminServer] Filled Email field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }

      // Look for "contact name" field and fill in contact name
      if (cellValue.includes('contact') && cellValue.includes('name')) {
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = 'Galen Adams';
          console.log(`[adminServer] Filled Contact Name field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }

      // Look for "country" field and fill in country
      if (cellValue === 'country') {
        const nextCell = worksheet.getCell(rowNumber, colNumber + 1);
        if (!nextCell.value || nextCell.value.toString().trim() === '') {
          nextCell.value = 'US';
          console.log(`[adminServer] Filled Country field at row ${rowNumber}, col ${colNumber + 1}`);
        }
      }
    });
  });

  console.log(`[adminServer] Order form data filled into existing header structure`);
}

// API endpoint to export Discraft order data back to Excel (legacy)
app.post('/api/discraft/export-orders', async (req, res) => {
  console.log('[adminServer] Exporting Discraft order data to Excel');

  // Check Supabase connection first
  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized',
      details: 'Database connection not available'
    });
  }

  try {

    // Define file paths
    const inputFile = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
    const now = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]; // YYYY-MM-DD-HH-MM-SS format
    const outputFile = path.join(__dirname, 'data', 'external data', `discraftstock_${now}.xlsx`);

    // Check if input file exists
    if (!fs.existsSync(inputFile)) {
      return res.status(400).json({
        success: false,
        error: 'Input file not found',
        details: `Expected file: ${inputFile}`
      });
    }

    // Get order data from the view with retry logic
    let orderData = null;
    let orderError = null;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        console.log(`[adminServer] Attempting to query view (attempt ${retryCount + 1}/${maxRetries})`);
        // Get ALL orderable products from the base table using pagination
        let allOrderableData = [];
        let from = 0;
        const pageSize = 1000;

        while (true) {
          const allOrderableResult = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1)
            .order('excel_mapping_key');

          if (allOrderableResult.error) {
            throw new Error(`Failed to query orderable data: ${allOrderableResult.error.message}`);
          }

          if (allOrderableResult.data.length === 0) break;

          allOrderableData = allOrderableData.concat(allOrderableResult.data);
          from += pageSize;

          if (allOrderableResult.data.length < pageSize) break; // Last page
        }

        // Get order quantities from the view for matched products only using pagination
        let viewOrderData = [];
        from = 0;

        while (true) {
          const viewResult = await supabase
            .from('v_stats_by_osl_discraft')
            .select('excel_mapping_key, "order"')
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1);

          if (viewResult.error) {
            throw new Error(`Failed to query view order data: ${viewResult.error.message}`);
          }

          if (viewResult.data.length === 0) break;

          viewOrderData = viewOrderData.concat(viewResult.data);
          from += pageSize;

          if (viewResult.data.length < pageSize) break; // Last page
        }

        // Create a map of order quantities by excel_mapping_key
        const orderMap = {};
        viewOrderData.forEach(item => {
          orderMap[item.excel_mapping_key] = item.order || 0;
        });

        // Combine all orderable products with their order quantities (defaulting to 0)
        const result = {
          data: allOrderableData.map(item => ({
            excel_mapping_key: item.excel_mapping_key,
            excel_column: item.excel_column,
            excel_row_hint: item.excel_row_hint,
            order: orderMap[item.excel_mapping_key] || 0  // Default to 0 if no order or not matched
          })),
          error: null
        };

        orderData = result.data;
        orderError = result.error;

        if (!orderError) {
          console.log(`[adminServer] Successfully retrieved ${orderData?.length || 0} records`);
          break;
        } else {
          console.log(`[adminServer] Query error on attempt ${retryCount + 1}:`, orderError);
        }
      } catch (err) {
        console.log(`[adminServer] Exception on attempt ${retryCount + 1}:`, err.message);
        orderError = err;
      }

      retryCount++;
      if (retryCount < maxRetries) {
        console.log(`[adminServer] Waiting 2 seconds before retry...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    if (orderError) {
      throw new Error(`Failed to query view after ${maxRetries} attempts: ${orderError.message || orderError}`);
    }

    console.log(`[adminServer] Found ${orderData.length} records (including 0 quantities for troubleshooting)`);

    // Read the original Excel file with ExcelJS for better formatting preservation
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(inputFile);
    const worksheet = workbook.getWorksheet(1); // First worksheet

    let recordsProcessed = 0;
    let orderQuantitiesUpdated = 0;



    console.log(`[adminServer] Excel file has ${worksheet.rowCount} rows`);

    // Create a map of order data by unique cell address (row + column)
    // This prevents overwrites when multiple weight ranges exist for the same row
    const orderMapByCell = {};
    orderData.forEach(row => {
      if (row.excel_row_hint && row.excel_column) {
        const cellKey = `${row.excel_column}${row.excel_row_hint}`;
        orderMapByCell[cellKey] = {
          order: row.order,
          excel_column: row.excel_column,
          excel_row_hint: row.excel_row_hint,
          excel_mapping_key: row.excel_mapping_key
        };
      }
    });

    console.log(`[adminServer] Created order map with ${Object.keys(orderMapByCell).length} entries`);
    console.log(`[adminServer] Sample cell keys:`, Object.keys(orderMapByCell).slice(0, 5));

    // Process using direct cell lookup with ExcelJS
    // This preserves formatting much better than XLSX library

    for (const [cellKey, orderInfo] of Object.entries(orderMapByCell)) {
      recordsProcessed++;

      // Map special column names to actual Excel columns
      let columnLetter = orderInfo.excel_column;
      if (columnLetter === 'ASSORTED') {
        columnLetter = 'L'; // Default to column L for assorted weights
      }

      // Get the cell using ExcelJS
      const cellAddress = columnLetter + orderInfo.excel_row_hint;
      const cell = worksheet.getCell(cellAddress);

      // Store the original style
      const originalStyle = { ...cell.style };

      // Update the cell value (default to 0 if no order)
      const orderValue = orderInfo.order || 0;
      cell.value = orderValue;

      // Restore the original style to preserve formatting
      cell.style = originalStyle;

      orderQuantitiesUpdated++;

      console.log(`[adminServer] ✅ Updated cell ${cellAddress} (${orderInfo.excel_mapping_key}) with order quantity: ${orderValue}`);
    }

    // Write the updated file with ExcelJS (preserves formatting)
    await workbook.xlsx.writeFile(outputFile);

    console.log(`[adminServer] Export completed: ${outputFile}`);

    res.json({
      success: true,
      outputFile: outputFile,
      recordsProcessed: recordsProcessed,
      orderQuantitiesUpdated: orderQuantitiesUpdated,
      orderDataCount: orderData.length
    });

  } catch (err) {
    console.error('[adminServer] Exception exporting Discraft orders:', err);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// API endpoint to download Discraft vendor file
app.post('/api/discraft/download-vendor-file', async (req, res) => {
  console.log('[adminServer] Downloading Discraft vendor file');

  try {
    const vendorUrl = 'https://www.discgolf.discraft.com/forms/stock.xlsx';
    const outputPath = 'data/external data/discraftstock.xlsx';

    console.log(`[adminServer] Downloading from: ${vendorUrl}`);
    console.log(`[adminServer] Saving to: ${outputPath}`);

    // Import fetch dynamically
    const fetch = (await import('node-fetch')).default;

    // Download the file
    const response = await fetch(vendorUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get the file buffer
    const buffer = await response.buffer();

    // Import fs dynamically
    const fs = (await import('fs')).default;
    const path = (await import('path')).default;

    // Ensure directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the file
    fs.writeFileSync(outputPath, buffer);

    console.log(`[adminServer] Successfully downloaded vendor file to: ${outputPath}`);
    console.log(`[adminServer] File size: ${buffer.length} bytes`);

    res.json({
      success: true,
      message: 'Discraft vendor file downloaded successfully',
      filePath: outputPath,
      fileSize: buffer.length,
      downloadUrl: vendorUrl
    });

  } catch (err) {
    console.error('[adminServer] Error downloading Discraft vendor file:', err);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

app.get('/api/discraft/review-parsing-issues', async (req, res) => {
  console.log('[adminServer] Analyzing Discraft parsing issues');
  try {
    const { data: unmatchedProducts, error: productsError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, min_weight, max_weight, is_currently_available')
      .is('calculated_mps_id', null)
      .eq('is_orderable', true)
      .limit(30);
    if (productsError) throw productsError;
    const { data: dbPlastics, error: plasticsError } = await supabase.from('t_plastics').select('plastic').eq('brand_id', 6);
    if (plasticsError) throw plasticsError;
    const { data: dbMolds, error: moldsError } = await supabase.from('t_molds').select('mold').eq('brand_id', 6);
    if (moldsError) throw moldsError;
    const vendorPlastics = [...new Set(unmatchedProducts.map(p => p.plastic_name))];
    const dbPlasticNames = dbPlastics.map(p => p.plastic);
    const plasticMismatches = vendorPlastics.map(vendorPlastic => {
      const suggestions = dbPlasticNames.filter(dbPlastic =>
        dbPlastic.toLowerCase().includes(vendorPlastic.toLowerCase()) ||
        vendorPlastic.toLowerCase().includes(dbPlastic.toLowerCase())
      );
      return { vendor_plastic: vendorPlastic, db_suggestions: suggestions };
    }).slice(0, 10);
    const vendorMolds = [...new Set(unmatchedProducts.map(p => p.mold_name))];
    const dbMoldNames = dbMolds.map(m => m.mold);
    const moldMismatches = vendorMolds.map(vendorMold => {
      const suggestions = dbMoldNames.filter(dbMold =>
        dbMold.toLowerCase().includes(vendorMold.toLowerCase()) ||
        vendorMold.toLowerCase().includes(dbMold.toLowerCase())
      );
      return { vendor_mold: vendorMold, db_suggestions: suggestions };
    }).slice(0, 10);
    res.json({ success: true, unmatchedVendorProducts: unmatchedProducts, plasticMismatches, moldMismatches });
  } catch (err) {
    console.error('[adminServer] Exception analyzing parsing issues:', err);
    res.status(500).json({ success: false, error: err.message });
  }
});

// Discraft Scheduler Management API Endpoints
app.get('/api/discraft/scheduler/status', async (req, res) => {
  console.log('[adminServer] Getting Discraft scheduler status');

  try {
    const { exec } = await import('child_process');
    const util = await import('util');
    const execAsync = util.promisify(exec);

    // Check PM2 status for discraft-scheduler
    const { stdout } = await execAsync('pm2 jlist');
    const processes = JSON.parse(stdout);
    const scheduler = processes.find(p => p.name === 'discraft-scheduler');

    if (!scheduler) {
      return res.json({
        success: true,
        status: 'not_found',
        message: 'Discraft scheduler not found in PM2'
      });
    }

    const isRunning = scheduler.pm2_env.status === 'online';
    const nextRun = scheduler.pm2_env.next_exec_time || null;

    res.json({
      success: true,
      status: isRunning ? 'running' : 'stopped',
      nextRun: nextRun,
      pid: scheduler.pid,
      uptime: scheduler.pm2_env.pm_uptime,
      restarts: scheduler.pm2_env.restart_time
    });

  } catch (err) {
    console.error('[adminServer] Error getting scheduler status:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/scheduler/start', async (req, res) => {
  console.log('[adminServer] Starting Discraft scheduler');

  try {
    const { exec } = await import('child_process');
    const util = await import('util');
    const execAsync = util.promisify(exec);

    // Start the scheduler using PM2
    await execAsync('pm2 start ecosystem.config.cjs --only discraft-scheduler');

    res.json({
      success: true,
      message: 'Discraft scheduler started successfully'
    });

  } catch (err) {
    console.error('[adminServer] Error starting scheduler:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/scheduler/stop', async (req, res) => {
  console.log('[adminServer] Stopping Discraft scheduler');

  try {
    const { exec } = await import('child_process');
    const util = await import('util');
    const execAsync = util.promisify(exec);

    // Stop the scheduler using PM2
    await execAsync('pm2 stop discraft-scheduler');

    res.json({
      success: true,
      message: 'Discraft scheduler stopped successfully'
    });

  } catch (err) {
    console.error('[adminServer] Error stopping scheduler:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/discraft/scheduler/threshold', async (req, res) => {
  console.log('[adminServer] Getting export threshold');

  try {
    const threshold = process.env.DISCRAFT_EXPORT_THRESHOLD || 100;

    res.json({
      success: true,
      threshold: parseInt(threshold)
    });

  } catch (err) {
    console.error('[adminServer] Error getting threshold:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/scheduler/threshold', async (req, res) => {
  console.log('[adminServer] Updating export threshold');

  try {
    const { threshold } = req.body;

    if (!threshold || threshold < 1) {
      return res.status(400).json({
        success: false,
        error: 'Valid threshold (minimum 1) is required'
      });
    }

    // Update the .env file
    const fs = await import('fs');
    const path = await import('path');
    const { fileURLToPath } = await import('url');
    const __dirname = path.dirname(fileURLToPath(import.meta.url));
    const envPath = path.join(__dirname, '.env');

    let envContent = fs.readFileSync(envPath, 'utf8');

    // Update or add the threshold
    if (envContent.includes('DISCRAFT_EXPORT_THRESHOLD=')) {
      envContent = envContent.replace(
        /DISCRAFT_EXPORT_THRESHOLD=\d+/,
        `DISCRAFT_EXPORT_THRESHOLD=${threshold}`
      );
    } else {
      envContent += `\nDISCRAFT_EXPORT_THRESHOLD=${threshold}`;
    }

    fs.writeFileSync(envPath, envContent);

    // Update the current process environment
    process.env.DISCRAFT_EXPORT_THRESHOLD = threshold;

    res.json({
      success: true,
      message: `Export threshold updated to ${threshold} discs`
    });

  } catch (err) {
    console.error('[adminServer] Error updating threshold:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/discraft/scheduler/logs', async (req, res) => {
  console.log('[adminServer] Getting Discraft scheduler logs');

  try {
    const { exec } = await import('child_process');
    const util = await import('util');
    const execAsync = util.promisify(exec);

    // Get PM2 logs for discraft-scheduler
    const { stdout } = await execAsync('pm2 logs discraft-scheduler --lines 50 --nostream');

    res.json({
      success: true,
      logs: stdout
    });

  } catch (err) {
    console.error('[adminServer] Error getting logs:', err);
    res.json({
      success: true,
      logs: `Error getting logs: ${err.message}`
    });
  }
});

app.post('/api/discraft/scheduler/clear-logs', async (req, res) => {
  console.log('[adminServer] Clearing Discraft scheduler logs');

  try {
    const { exec } = await import('child_process');
    const util = await import('util');
    const execAsync = util.promisify(exec);

    // Clear PM2 logs for discraft-scheduler
    await execAsync('pm2 flush discraft-scheduler');

    res.json({
      success: true,
      message: 'Logs cleared successfully'
    });

  } catch (err) {
    console.error('[adminServer] Error clearing logs:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/discraft/test-automation', async (req, res) => {
  console.log('[adminServer] Running Discraft automation test');

  try {
    // Import and run the automation
    const runDiscraftDailyAutomation = (await import('./discraftDailyAutomation.js')).default;

    await runDiscraftDailyAutomation();

    res.json({
      success: true,
      message: 'Test automation completed successfully',
      totalDiscs: 'Check logs for details',
      emailSent: 'Check logs for details',
      exportCreated: 'Check logs for details'
    });

  } catch (err) {
    console.error('[adminServer] Error running test automation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to get MPS review data
app.get('/api/mps-review', async (req, res) => {
  console.log('[adminServer] Getting MPS review data');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Query for active MPS records with no stock using a raw SQL query
    const { data, error } = await supabase.rpc('get_mps_review_data');

    if (error) {
      console.error('[adminServer] Error getting MPS review data:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Data is already in the correct format from the function
    const records = data || [];

    console.log(`[adminServer] Retrieved ${records.length} MPS records for review`);

    res.json({
      success: true,
      records: records
    });
  } catch (err) {
    console.error('[adminServer] Exception getting MPS review data:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to mark MPS as inactive
app.post('/api/mps-mark-inactive', async (req, res) => {
  console.log('[adminServer] Marking MPS as inactive');

  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'MPS ID is required'
      });
    }

    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({ active: false })
      .eq('id', mpsId)
      .select();

    if (error) {
      console.error('[adminServer] Error marking MPS as inactive:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Successfully marked MPS ID ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ID ${mpsId} marked as inactive successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception marking MPS as inactive:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to get plastic pricing data
app.get('/api/plastic-pricing', async (req, res) => {
  console.log('[adminServer] Getting plastic pricing data');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Let's try a simpler approach - use a single query with JOIN
    const { data, error } = await supabase
      .from('t_plastics')
      .select(`
        id,
        plastic,
        val_order_cost,
        val_map_price,
        val_retail_price,
        val_msrp,
        val_max_amazon_price,
        price_cost_verified_at,
        v_stats_by_plastic!inner(discs_in_stock_and_uploaded)
      `)
      .order('id', { ascending: true });

    if (error) {
      console.error('[adminServer] Error getting plastic data with JOIN:', error);

      // Fallback to separate queries if JOIN doesn't work
      console.log('[adminServer] Trying fallback approach with separate queries...');

      // Get stats data
      const { data: statsData, error: statsError } = await supabase
        .from('v_stats_by_plastic')
        .select('plastic_id, discs_in_stock_and_uploaded')
        .order('plastic_id', { ascending: true });

      if (statsError) {
        console.error('[adminServer] Error getting stats data:', statsError);
        return res.status(500).json({
          success: false,
          error: statsError.message
        });
      }

      // Get pricing data
      const { data: pricingData, error: pricingError } = await supabase
        .from('t_plastics')
        .select('id, plastic, val_order_cost, val_map_price, val_retail_price, val_msrp, val_max_amazon_price, price_cost_verified_at')
        .order('id', { ascending: true });

      if (pricingError) {
        console.error('[adminServer] Error getting pricing data:', pricingError);
        return res.status(500).json({
          success: false,
          error: pricingError.message
        });
      }

      console.log('[adminServer] Stats data sample:', statsData.slice(0, 3));
      console.log('[adminServer] Pricing data sample:', pricingData.slice(0, 3));

      // Merge the data
      const mergedData = pricingData.map(plastic => {
        const stats = statsData.find(stat => stat.plastic_id === plastic.id);
        return {
          id: plastic.id,
          plastic: plastic.plastic,
          val_order_cost: plastic.val_order_cost,
          val_map_price: plastic.val_map_price,
          val_retail_price: plastic.val_retail_price,
          val_msrp: plastic.val_msrp,
          val_max_amazon_price: plastic.val_max_amazon_price,
          price_cost_verified_at: plastic.price_cost_verified_at,
          discs_in_stock_and_uploaded: stats ? stats.discs_in_stock_and_uploaded : 0
        };
      });

      console.log('[adminServer] Merged data sample:', mergedData.slice(0, 3));

      res.json({
        success: true,
        records: mergedData
      });
      return;
    }

    console.log('[adminServer] JOIN query successful, data sample:', data.slice(0, 3));

    res.json({
      success: true,
      records: mergedData
    });
  } catch (err) {
    console.error('[adminServer] Exception getting plastic pricing data:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to update plastic pricing field
app.put('/api/plastic-pricing/update', async (req, res) => {
  console.log('[adminServer] Updating plastic pricing field');

  try {
    const { id, field, value } = req.body;

    if (!id || !field) {
      return res.status(400).json({
        success: false,
        error: 'ID and field are required'
      });
    }

    // Validate field name for security
    const allowedFields = ['val_order_cost', 'val_map_price', 'val_retail_price', 'val_msrp', 'val_max_amazon_price'];
    if (!allowedFields.includes(field)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid field name'
      });
    }

    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Update the plastic record
    const updateData = {};
    updateData[field] = value;

    const { data, error } = await supabase
      .from('t_plastics')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) {
      console.error('[adminServer] Error updating plastic field:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Plastic record not found'
      });
    }

    console.log(`[adminServer] Successfully updated plastic ID ${id} field ${field} to ${value}`);

    res.json({
      success: true,
      message: `Plastic field ${field} updated successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception updating plastic field:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to verify plastic cost/price
app.post('/api/plastic-pricing/verify', async (req, res) => {
  console.log('[adminServer] Verifying plastic cost/price');

  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({
        success: false,
        error: 'Plastic ID is required'
      });
    }

    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Update the plastic record to set price_cost_verified_at to now
    const { data, error } = await supabase
      .from('t_plastics')
      .update({ price_cost_verified_at: new Date().toISOString() })
      .eq('id', id)
      .select();

    if (error) {
      console.error('[adminServer] Error verifying plastic cost/price:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Plastic record not found'
      });
    }

    console.log(`[adminServer] Successfully verified cost/price for plastic ID ${id}`);

    res.json({
      success: true,
      message: `Plastic ID ${id} cost/price verified successfully`,
      updatedRecord: data[0]
    });
  } catch (err) {
    console.error('[adminServer] Exception verifying plastic cost/price:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to get SDASINS data
app.get('/api/sdasins', async (req, res) => {
  try {
    const { rankBelow, idAbove, notesNotContain, notesNotNull, colorIsNull } = req.query;

    console.log('[adminServer] Fetching SDASINS data with filters:', req.query);
    console.log('[adminServer] Individual filter values:', { rankBelow, idAbove, notesNotContain, notesNotNull, colorIsNull });

    let allRecords = [];
    let offset = 0;
    const batchSize = 1000;
    let hasMore = true;

    // Build the base query with filters and join with t_colors
    let baseQuery = supabase
      .from('t_sdasins')
      .select(`
        id, parent_asin, asin, notes, raw_notes, parsed_brand, parsed_mold, parsed_plastic, parsed_stamp,
        parsed_min_weight, parsed_max_weight, color_id, so_rank_30day_avg, so_rank_30day_avg_date, fbm_uploaded_at,
        t_colors(color)
      `)
      .order('id', { ascending: true });

    // Apply filters to base query
    if (rankBelow) {
      const rankValue = parseFloat(rankBelow);
      if (!isNaN(rankValue)) {
        console.log(`[adminServer] Applying rank filter: < ${rankValue}`);
        baseQuery = baseQuery.lt('so_rank_30day_avg', rankValue);
      }
    }

    if (idAbove) {
      const idValue = parseInt(idAbove);
      if (!isNaN(idValue)) {
        console.log(`[adminServer] Applying ID filter: > ${idValue}`);
        baseQuery = baseQuery.gt('id', idValue);
      }
    }

    if (notesNotContain) {
      console.log(`[adminServer] Applying notes not contain filter: "${notesNotContain}"`);
      baseQuery = baseQuery.not('notes', 'ilike', `%${notesNotContain}%`);
    }

    if (notesNotNull === 'true') {
      console.log(`[adminServer] Applying notes not null filter`);
      baseQuery = baseQuery.not('notes', 'is', null).neq('notes', '');
    }

    if (colorIsNull === 'true') {
      console.log(`[adminServer] Applying color is null filter`);
      baseQuery = baseQuery.is('color_id', null);
    }

    while (hasMore) {
      console.log(`[adminServer] Fetching SDASINS batch starting at offset ${offset}...`);

      // Apply pagination to the filtered query
      const query = baseQuery.range(offset, offset + batchSize - 1);

      const { data, error } = await query;

      if (error) {
        console.error('[adminServer] Error fetching SDASINS data:', error);
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }

      if (!data || data.length === 0) {
        hasMore = false;
        break;
      }

      allRecords = allRecords.concat(data);
      console.log(`[adminServer] Fetched ${data.length} records in this batch. Total so far: ${allRecords.length}`);

      // If we got fewer records than the batch size, we've reached the end
      if (data.length < batchSize) {
        hasMore = false;
      } else {
        offset += batchSize;
      }
    }

    console.log(`[adminServer] Successfully fetched ${allRecords.length} SDASINS records with filters`);

    res.json({
      success: true,
      records: allRecords,
      totalRecords: allRecords.length,
      appliedFilters: req.query
    });

  } catch (err) {
    console.error('[adminServer] Exception fetching SDASINS data:', err.message);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to get color options
app.get('/api/colors', async (req, res) => {
  try {
    console.log('[adminServer] Fetching color options...');

    const { data, error } = await supabase
      .from('t_colors')
      .select('id, color')
      .order('color', { ascending: true });

    if (error) {
      console.error('[adminServer] Error fetching color options:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    console.log(`[adminServer] Successfully fetched ${data?.length || 0} color options`);

    res.json({
      success: true,
      colors: data || []
    });

  } catch (err) {
    console.error('[adminServer] Exception fetching color options:', err.message);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to create new SDASINS record or update existing parent ASIN
app.post('/api/sdasins/create', async (req, res) => {
  try {
    const { parent_asin, asin, notes } = req.body;

    if (!asin) {
      return res.status(400).json({
        success: false,
        error: 'ASIN is required'
      });
    }

    console.log(`[adminServer] Creating/updating SDASINS record with ASIN: ${asin}`);

    // First, check if a record with this ASIN already exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_sdasins')
      .select('*')
      .eq('asin', asin)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('[adminServer] Error checking for existing ASIN:', checkError);
      return res.status(500).json({
        success: false,
        error: checkError.message
      });
    }

    if (existingRecord) {
      // Record exists - update the parent ASIN and notes if provided
      console.log(`[adminServer] ASIN ${asin} already exists (ID: ${existingRecord.id}), updating parent ASIN to: ${parent_asin}`);

      const updateData = {};
      if (parent_asin !== undefined) {
        updateData.parent_asin = parent_asin || null;
      }
      if (notes !== undefined && notes !== null && notes.toString().trim() !== '') {
        updateData.notes = notes;
      }

      const { data: updatedRecord, error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', existingRecord.id)
        .select();

      if (updateError) {
        console.error('[adminServer] Error updating existing SDASINS record:', updateError);
        return res.status(500).json({
          success: false,
          error: updateError.message
        });
      }

      console.log(`[adminServer] Successfully updated existing SDASINS record:`, updatedRecord[0]);

      res.json({
        success: true,
        record: updatedRecord[0],
        action: 'updated',
        message: `Found existing ASIN ${asin} and updated parent ASIN to: ${parent_asin || 'null'}`
      });

    } else {
      // Record doesn't exist - create new one
      console.log(`[adminServer] ASIN ${asin} not found, creating new record`);

      const { data: newRecord, error: insertError } = await supabase
        .from('t_sdasins')
        .insert([{
          parent_asin: parent_asin || null,
          asin: asin,
          notes: notes || null
        }])
        .select();

      if (insertError) {
        console.error('[adminServer] Error creating SDASINS record:', insertError);
        return res.status(500).json({
          success: false,
          error: insertError.message
        });
      }

      console.log(`[adminServer] Successfully created new SDASINS record:`, newRecord[0]);

      res.json({
        success: true,
        record: newRecord[0],
        action: 'created',
        message: `Created new record with ASIN: ${asin}`
      });
    }

  } catch (err) {
    console.error('[adminServer] Exception creating/updating SDASINS record:', err.message);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to update SDASINS field
app.put('/api/sdasins/update', async (req, res) => {
  try {
    const { id, field, value } = req.body;

    if (!id || !field) {
      return res.status(400).json({
        success: false,
        error: 'ID and field are required'
      });
    }

    // Validate field name to prevent SQL injection
    const allowedFields = ['parent_asin', 'notes', 'raw_notes', 'parsed_brand', 'parsed_mold', 'parsed_plastic', 'parsed_stamp', 'parsed_min_weight', 'parsed_max_weight', 'color_id'];
    if (!allowedFields.includes(field)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid field name'
      });
    }

    console.log(`[adminServer] Updating SDASINS ${id} field ${field} to:`, value);

    // Prepare the update object
    const updateData = {};
    updateData[field] = value;

    const { data, error } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('[adminServer] Error updating SDASINS field:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    console.log(`[adminServer] Successfully updated SDASINS ${id} field ${field}`);

    res.json({
      success: true,
      message: 'SDASINS field updated successfully'
    });

  } catch (err) {
    console.error('[adminServer] Exception updating SDASINS field:', err.message);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Add this API endpoint to handle Amazon FBA import
app.post('/api/import-amazon-fba', async (req, res) => {
  console.log('[adminServer] Running Amazon FBA import');

  try {
    // Import the Amazon import function
    const { importAmazonInventoryLedger } = await import('./importAmazonInventoryLedger.js');

    // Run the import
    const result = await importAmazonInventoryLedger();

    console.log(`[adminServer] Amazon FBA import completed: ${result.message}`);

    return res.json({
      success: true,
      message: result.message,
      details: result.details,
      importCount: result.importCount
    });
  } catch (err) {
    console.error(`[adminServer] Exception running Amazon FBA import: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle FBA carrying costs refresh
app.post('/api/refresh-fba-carrying-costs', async (req, res) => {
  console.log('[adminServer] Refreshing FBA carrying costs materialized view');

  try {
    const startTime = Date.now();

    // Since Supabase doesn't allow direct REFRESH MATERIALIZED VIEW commands,
    // we'll use a stored procedure approach or manual refresh
    console.log('[adminServer] Attempting to refresh materialized view via stored procedure');

    // Try to call a stored procedure that refreshes the materialized view
    const { data, error } = await supabase.rpc('refresh_mv_sdasin_avg_carrying_cost_fba');

    if (error) {
      console.log('[adminServer] Stored procedure not found, materialized view may need manual refresh');
      console.log('[adminServer] Error:', error.message);

      // For now, we'll just get the current count and inform the user
      const { count, error: countError } = await supabase
        .from('mv_sdasin_avg_carrying_cost_fba')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        throw new Error(`Failed to access materialized view: ${countError.message}`);
      }

      const endTime = Date.now();
      const duration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

      return res.json({
        success: false,
        error: 'Materialized view refresh requires database admin privileges',
        details: `The materialized view currently has ${count || 'unknown'} records. To refresh it, you need to run "REFRESH MATERIALIZED VIEW mv_sdasin_avg_carrying_cost_fba;" directly in your database admin panel.`,
        recordCount: count || 'Unknown',
        duration: duration
      });
    }

    // If the stored procedure worked, get the updated count
    const { count, error: countError } = await supabase
      .from('mv_sdasin_avg_carrying_cost_fba')
      .select('*', { count: 'exact', head: true });

    const recordCount = countError ? 'Unknown' : count;

    const endTime = Date.now();
    const duration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] FBA carrying costs refresh completed in ${duration}`);

    return res.json({
      success: true,
      message: 'Materialized view refreshed successfully',
      recordCount: recordCount,
      duration: duration,
      details: `Refreshed mv_sdasin_avg_carrying_cost_fba with ${recordCount} records`
    });
  } catch (err) {
    console.error(`[adminServer] Exception refreshing FBA carrying costs: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle FBA inventory report generation
app.post('/api/generate-fba-inventory-report', async (req, res) => {
  console.log('[adminServer] Generating FBA inventory value report');

  try {
    const startTime = Date.now();

    // Call the stored procedure to generate the report
    const { data, error } = await supabase.rpc('fn_upsert_monthly_fba_inventory_value');

    if (error) {
      console.log('[adminServer] Stored procedure error:', error.message);

      // If the function doesn't exist, provide helpful error message
      if (error.message.includes('function') && error.message.includes('does not exist')) {
        return res.json({
          success: false,
          error: 'FBA inventory report function not found',
          details: `The function fn_upsert_monthly_fba_inventory_value() does not exist. Please run the SQL from update_fba_inventory_function.sql in your Supabase SQL Editor to create it.`
        });
      }

      throw new Error(`Failed to generate FBA inventory report: ${error.message}`);
    }

    // Extract results from the function return
    const result = data && data.length > 0 ? data[0] : {};

    const endTime = Date.now();
    const totalDuration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] FBA inventory report completed in ${totalDuration}`);
    console.log(`[adminServer] Report results:`, result);

    // Format the report month for display
    const reportMonth = result.report_month ?
      new Date(result.report_month).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long'
      }) : 'Unknown';

    // Format processing time
    const processingTime = result.processing_time || totalDuration;

    return res.json({
      success: true,
      message: 'FBA inventory report generated successfully',
      reportMonth: reportMonth,
      processedRecords: result.processed_records || 0,
      totalUnits: result.total_units || 0,
      totalValue: result.total_value || 0,
      weightedAvgCost: result.weighted_avg_cost || 0,
      processingTime: processingTime,
      details: `Generated report for ${reportMonth}\n` +
               `Processed ${result.processed_records || 0} records\n` +
               `Total FBA Units: ${(result.total_units || 0).toLocaleString()}\n` +
               `Total FBA Value: $${(result.total_value || 0).toLocaleString()}\n` +
               `Weighted Avg Cost: $${result.weighted_avg_cost || 0}\n` +
               `Processing Time: ${processingTime}`
    });
  } catch (err) {
    console.error(`[adminServer] Exception generating FBA inventory report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add this API endpoint to handle historical FBA reports generation
app.post('/api/generate-historical-fba-reports', async (req, res) => {
  console.log('[adminServer] Generating historical FBA inventory value reports');

  try {
    const startTime = Date.now();

    // Call the stored procedure to generate historical reports
    const { data, error } = await supabase.rpc('fn_generate_historical_fba_inventory_values');

    if (error) {
      console.log('[adminServer] Historical reports stored procedure error:', error.message);

      // If the function doesn't exist, provide helpful error message
      if (error.message.includes('function') && error.message.includes('does not exist')) {
        return res.json({
          success: false,
          error: 'Historical FBA reports function not found',
          details: `The function fn_generate_historical_fba_inventory_values() does not exist. Please run the SQL from create_historical_fba_function.sql in your Supabase SQL Editor to create it.`
        });
      }

      throw new Error(`Failed to generate historical FBA reports: ${error.message}`);
    }

    // Extract results from the function return
    const result = data && data.length > 0 ? data[0] : {};

    const endTime = Date.now();
    const totalDuration = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    console.log(`[adminServer] Historical FBA reports completed in ${totalDuration}`);
    console.log(`[adminServer] Historical results:`, result);

    // Format processing time
    const processingTime = result.processing_time || totalDuration;

    return res.json({
      success: true,
      message: 'Historical FBA reports generated successfully',
      monthsProcessed: result.months_processed || 0,
      monthsLocked: result.months_locked || 0,
      totalRecordsProcessed: result.total_records_processed || 0,
      processingTime: processingTime,
      details: result.details || 'No detailed information available'
    });
  } catch (err) {
    console.error(`[adminServer] Exception generating historical FBA reports: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to get FBA reports for lock management
app.get('/api/get-fba-reports', async (req, res) => {
  console.log('[adminServer] Getting FBA reports for lock management');

  try {
    // Get all FBA reports ordered by month
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .select('*')
      .order('month', { ascending: false });

    if (error) {
      throw new Error(`Failed to get FBA reports: ${error.message}`);
    }

    console.log(`[adminServer] Retrieved ${data?.length || 0} FBA reports`);

    return res.json({
      success: true,
      reports: data || []
    });
  } catch (err) {
    console.error(`[adminServer] Exception getting FBA reports: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to lock an FBA report
app.post('/api/lock-fba-report', async (req, res) => {
  console.log('[adminServer] Locking FBA report');

  try {
    const { month } = req.body;

    if (!month) {
      return res.status(400).json({
        success: false,
        error: 'Month parameter is required'
      });
    }

    // Update the report to set locked_at timestamp
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .update({
        locked_at: new Date().toISOString()
      })
      .eq('month', month)
      .select();

    if (error) {
      throw new Error(`Failed to lock FBA report: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Report not found for the specified month'
      });
    }

    console.log(`[adminServer] Successfully locked FBA report for month: ${month}`);

    return res.json({
      success: true,
      message: `Successfully locked report for ${month}`,
      report: data[0]
    });
  } catch (err) {
    console.error(`[adminServer] Exception locking FBA report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to unlock an FBA report
app.post('/api/unlock-fba-report', async (req, res) => {
  console.log('[adminServer] Unlocking FBA report');

  try {
    const { month } = req.body;

    if (!month) {
      return res.status(400).json({
        success: false,
        error: 'Month parameter is required'
      });
    }

    // Update the report to clear locked_at timestamp
    const { data, error } = await supabase
      .from('rpt_amaz_monthly_fba_inventory_value')
      .update({
        locked_at: null
      })
      .eq('month', month)
      .select();

    if (error) {
      throw new Error(`Failed to unlock FBA report: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Report not found for the specified month'
      });
    }

    console.log(`[adminServer] Successfully unlocked FBA report for month: ${month}`);

    return res.json({
      success: true,
      message: `Successfully unlocked report for ${month}`,
      report: data[0]
    });
  } catch (err) {
    console.error(`[adminServer] Exception unlocking FBA report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to test Amazon SP-API connection
app.post('/api/test-amazon-connection', async (req, res) => {
  console.log('[adminServer] Testing Amazon SP-API connection');

  try {
    // Import the Amazon client
    const { default: AmazonSpApiClient } = await import('./amazonSpApiClient.js');

    // Create client and test connection
    const amazonClient = new AmazonSpApiClient();
    const result = await amazonClient.testConnection();

    console.log(`[adminServer] Amazon connection test result:`, result);

    return res.json(result);
  } catch (err) {
    console.error(`[adminServer] Exception testing Amazon connection: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to enqueue Amazon listing deletion task
app.post('/api/delete-amazon-listing', async (req, res) => {
  console.log('[adminServer] Enqueueing Amazon listing deletion task');

  try {
    const { sku, marketplaceIds, reason } = req.body;

    if (!sku) {
      return res.status(400).json({
        success: false,
        error: 'SKU parameter is required'
      });
    }

    // Import the enqueue function
    const { enqueueDeleteAmazonListingTask } = await import('./processDeleteAmazonListingTask.js');

    // Enqueue the task
    const task = await enqueueDeleteAmazonListingTask(
      supabase,
      sku,
      marketplaceIds,
      reason
    );

    console.log(`[adminServer] Successfully enqueued Amazon listing deletion task ${task.id} for SKU: ${sku}`);

    return res.json({
      success: true,
      message: `Successfully enqueued Amazon listing deletion task for SKU: ${sku}`,
      taskId: task.id,
      sku: sku,
      marketplaceIds: marketplaceIds || ['ATVPDKIKX0DER'],
      reason: reason || 'Manual deletion via admin interface'
    });
  } catch (err) {
    console.error(`[adminServer] Exception enqueueing Amazon listing deletion: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to import Amazon Fulfilled Inventory Report
app.post('/api/import-fulfilled-inventory', async (req, res) => {
  console.log('[adminServer] Importing Amazon Fulfilled Inventory Report');

  try {
    // Import the function
    const { importAmazonFulfilledInventory } = await import('./importAmazonFulfilledInventory.js');

    // Call the import function with the Supabase client
    const result = await importAmazonFulfilledInventory(supabase);

    console.log(`[adminServer] Fulfilled inventory import completed: ${result.importCount} records imported`);

    return res.json({
      success: true,
      message: result.message,
      details: result.details,
      importCount: result.importCount,
      skippedCount: result.skippedCount
    });
  } catch (err) {
    console.error(`[adminServer] Exception importing fulfilled inventory: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to update Keepa Best Sellers ranks
app.post('/api/update-keepa-ranks', async (req, res) => {
  console.log('[adminServer] Updating Keepa Best Sellers ranks');

  try {
    const startTime = Date.now();

    // Import and run the Keepa update script
    const { spawn } = await import('child_process');

    // Run the update_keepa_ranks.js script
    const keepaProcess = spawn('node', ['update_keepa_ranks.js'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let output = '';
    let errorOutput = '';

    // Collect output from the script
    keepaProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    keepaProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // Wait for the process to complete
    const exitCode = await new Promise((resolve) => {
      keepaProcess.on('close', resolve);
    });

    const endTime = Date.now();
    const processingTime = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    if (exitCode !== 0) {
      console.error(`[adminServer] Keepa update script failed with exit code ${exitCode}`);
      console.error(`[adminServer] Error output:`, errorOutput);

      return res.status(500).json({
        success: false,
        error: `Keepa update script failed with exit code ${exitCode}`,
        details: errorOutput || output
      });
    }

    // Parse the output to extract key metrics
    const lines = output.split('\n');
    let totalProcessed = 0;
    let updatedCount = 0;
    let newAsinCount = 0;
    let notFoundCount = 0;
    let categoryId = '';
    let tokensConsumed = 0;
    let updateTimestamp = '';

    // Extract information from the output
    for (const line of lines) {
      if (line.includes('Total ASINs processed:')) {
        totalProcessed = parseInt(line.match(/(\d+)/)?.[1] || '0');
      }
      if (line.includes('Successfully updated:')) {
        updatedCount = parseInt(line.match(/(\d+)/)?.[1] || '0');
      }
      if (line.includes('New competitor ASINs added:')) {
        newAsinCount = parseInt(line.match(/(\d+)/)?.[1] || '0');
      }
      if (line.includes('Not found in database:')) {
        notFoundCount = parseInt(line.match(/(\d+)/)?.[1] || '0');
      }
      if (line.includes('Category ID:')) {
        categoryId = line.split('Category ID:')[1]?.trim() || '';
      }
      if (line.includes('tokensConsumed')) {
        const match = line.match(/"tokensConsumed":\s*(\d+)/);
        if (match) tokensConsumed = parseInt(match[1]);
      }
      if (line.includes('Update timestamp:')) {
        updateTimestamp = line.split('Update timestamp:')[1]?.trim() || '';
      }
    }

    console.log(`[adminServer] Keepa ranks update completed in ${processingTime}`);
    console.log(`[adminServer] Updated ${updatedCount} out of ${totalProcessed} ASINs`);

    return res.json({
      success: true,
      message: 'Keepa Best Sellers ranks updated successfully',
      totalProcessed: totalProcessed,
      updatedCount: updatedCount,
      newAsinCount: newAsinCount,
      notFoundCount: notFoundCount,
      categoryId: categoryId || '3406051',
      tokensConsumed: tokensConsumed,
      processingTime: processingTime,
      updateTimestamp: updateTimestamp || new Date().toISOString(),
      details: output
    });
  } catch (err) {
    console.error(`[adminServer] Exception updating Keepa ranks: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to reset FBM inactive records
app.post('/api/reset-fbm-inactive-records', async (req, res) => {
  console.log('[adminServer] Resetting FBM inactive records');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Import and run the reset function
    const { resetFbmInactiveRecords } = await import('./resetFbmInactiveRecords.js');

    // Call the reset function with the Supabase client
    const result = await resetFbmInactiveRecords(supabase);

    if (result.success) {
      console.log(`[adminServer] FBM inactive records reset completed: ${result.recordsUpdated} records updated`);

      return res.json({
        success: true,
        message: result.message,
        recordsFound: result.recordsFound,
        recordsUpdated: result.recordsUpdated,
        details: result.details
      });
    } else {
      console.error(`[adminServer] FBM inactive records reset failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error,
        recordsFound: result.recordsFound || 0,
        recordsUpdated: result.recordsUpdated || 0,
        viewStructure: result.viewStructure
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception resetting FBM inactive records: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to reset FBA inactive records
app.post('/api/reset-fba-inactive-records', async (req, res) => {
  console.log('[adminServer] Resetting FBA inactive records');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Import and run the reset function
    const { resetFbaInactiveRecords } = await import('./resetFbaInactiveRecords.js');

    // Call the reset function with the Supabase client
    const result = await resetFbaInactiveRecords(supabase);

    if (result.success) {
      console.log(`[adminServer] FBA inactive records reset completed: ${result.recordsUpdated} records updated`);

      return res.json({
        success: true,
        message: result.message,
        recordsFound: result.recordsFound,
        recordsUpdated: result.recordsUpdated,
        errors: result.errors,
        details: result.details
      });
    } else {
      console.error(`[adminServer] FBA inactive records reset failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error,
        recordsFound: result.recordsFound || 0,
        recordsUpdated: result.recordsUpdated || 0,
        viewStructure: result.viewStructure
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception resetting FBA inactive records: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Add API endpoint to generate FBA ship-in recommendations
app.post('/api/generate-fba-recommendations', async (req, res) => {
  console.log('[adminServer] Generating FBA ship-in recommendations');

  try {
    const startTime = Date.now();

    // Import and run the FBA recommendations script
    const { spawn } = await import('child_process');

    // Run the generate_fba_ship_in_recommendations.js script
    const fbaProcess = spawn('node', ['generate_fba_ship_in_recommendations.js'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let output = '';
    let errorOutput = '';

    // Collect output from the script
    fbaProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    fbaProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // Wait for the process to complete
    const exitCode = await new Promise((resolve) => {
      fbaProcess.on('close', resolve);
    });

    const endTime = Date.now();
    const processingTime = `${((endTime - startTime) / 1000).toFixed(2)} seconds`;

    if (exitCode !== 0) {
      console.error(`[adminServer] FBA recommendations script failed with exit code ${exitCode}`);
      console.error(`[adminServer] Error output:`, errorOutput);

      return res.status(500).json({
        success: false,
        error: `FBA recommendations script failed with exit code ${exitCode}`,
        details: errorOutput || output
      });
    }

    // Parse the output to extract key metrics
    const lines = output.split('\n');
    let totalAnalyzed = 0;
    let recordsProcessed = 0;
    let createdCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    // Breakdown by recommendation level
    const breakdown = {
      rank_300: 0,   // 5 units
      rank_600: 0,   // 4 units
      rank_1000: 0,  // 3 units
      rank_1500: 0,  // 2 units
      rank_2130: 0,  // 1 unit
      skipped: 0     // > 2129
    };

    // Extract information from the output
    for (const line of lines) {
      if (line.includes('total FBA-enabled sdasins')) {
        const match = line.match(/Found (\d+) total FBA-enabled sdasins/);
        if (match) totalAnalyzed = parseInt(match[1]);
      }
      if (line.includes('Records processed:')) {
        const match = line.match(/Records processed: (\d+)/);
        if (match) recordsProcessed = parseInt(match[1]);
      }
      if (line.includes('New records created:')) {
        const match = line.match(/New records created: (\d+)/);
        if (match) createdCount = parseInt(match[1]);
      }
      if (line.includes('Existing records updated:')) {
        const match = line.match(/Existing records updated: (\d+)/);
        if (match) updatedCount = parseInt(match[1]);
      }
      if (line.includes('Errors encountered:')) {
        const match = line.match(/Errors encountered: (\d+)/);
        if (match) errorCount = parseInt(match[1]);
      }

      // Count recommendations by quantity (which corresponds to rank ranges)
      if (line.includes('→ 5 units')) breakdown.rank_300++;
      if (line.includes('→ 4 units')) breakdown.rank_600++;
      if (line.includes('→ 3 units')) breakdown.rank_1000++;
      if (line.includes('→ 2 units')) breakdown.rank_1500++;
      if (line.includes('→ 1 units')) breakdown.rank_2130++;
    }

    console.log(`[adminServer] FBA recommendations completed in ${processingTime}`);
    console.log(`[adminServer] Processed ${recordsProcessed} records, created ${createdCount}, updated ${updatedCount}`);

    return res.json({
      success: true,
      message: 'FBA ship-in recommendations generated successfully',
      totalAnalyzed: totalAnalyzed,
      recordsProcessed: recordsProcessed,
      createdCount: createdCount,
      updatedCount: updatedCount,
      errorCount: errorCount,
      processingTime: processingTime,
      breakdown: breakdown,
      details: output
    });
  } catch (err) {
    console.error(`[adminServer] Exception generating FBA recommendations: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      details: err.stack
    });
  }
});

// Innova API endpoints
app.post('/api/import-innova-data', async (req, res) => {
  console.log('[adminServer] Importing Innova data');

  try {
    // Import the function
    const { importInnovaOrderData } = await import('./importInnovaOrder.js');

    // Call the import function
    const result = await importInnovaOrderData();

    if (result.success) {
      console.log(`[adminServer] Innova import completed: ${result.totalRecords} records imported`);

      return res.json({
        success: true,
        message: 'Innova data imported successfully',
        totalRecords: result.totalRecords,
        batchId: result.batchId
      });
    } else {
      console.error(`[adminServer] Innova import failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception importing Innova data: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/view-innova-data', async (req, res) => {
  console.log('[adminServer] Viewing Innova data');

  try {
    if (!supabase) {
      return res.status(500).json({ error: 'Supabase client not initialized' });
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('it_innova_order_sheet_lines')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error(`[adminServer] Error counting Innova records: ${countError.message}`);
      return res.status(500).json({
        success: false,
        error: countError.message
      });
    }

    // Get sample data (first 10 records)
    const { data: sampleData, error: dataError } = await supabase
      .from('it_innova_order_sheet_lines')
      .select('*')
      .order('id')
      .limit(10);

    if (dataError) {
      console.error(`[adminServer] Error getting Innova sample data: ${dataError.message}`);
      return res.status(500).json({
        success: false,
        error: dataError.message
      });
    }

    console.log(`[adminServer] Retrieved ${sampleData.length} sample records from ${count} total records`);

    return res.json({
      success: true,
      totalCount: count,
      sampleData: sampleData
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing Innova data: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Innova validation endpoints
app.post('/api/validate-innova-matches', async (req, res) => {
  console.log('[adminServer] Validating Innova matches');

  try {
    // Import the validation function
    const { runValidation } = await import('./validateInnovaMatches.js');

    // Get auto-clear preference from request body (default: false for safety)
    const autoClearBrokenLinks = req.body?.autoClearBrokenLinks === true;
    console.log(`[adminServer] Auto-clear broken links: ${autoClearBrokenLinks}`);

    // Run the validation
    const result = await runValidation(autoClearBrokenLinks);

    if (result.success) {
      console.log(`[adminServer] Validation completed: ${result.totalMatches} matches processed`);

      return res.json({
        success: true,
        message: 'Validation completed successfully',
        totalMatches: result.totalMatches,
        validMatches: result.validMatches,
        questionableMatches: result.questionableMatches,
        brokenLinks: result.brokenLinks,
        autoCleared: result.autoCleared
      });
    } else {
      console.error(`[adminServer] Validation failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception validating Innova matches: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/view-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing validation report');

  try {
    // Import the function to get validation results
    const { getValidationResults } = await import('./validateInnovaMatches.js');

    // Get the stored validation results
    const validationResults = getValidationResults();

    if (!validationResults) {
      return res.json({
        success: true,
        message: 'No validation results found. Run validation first.',
        reportData: []
      });
    }

    console.log(`[adminServer] Retrieved ${validationResults.length} validation records`);

    return res.json({
      success: true,
      reportData: validationResults.slice(0, 50) // Limit to first 50 for display
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Enhanced Innova matching endpoint
app.post('/api/enhanced-innova-matching', async (req, res) => {
  console.log('[adminServer] Running enhanced Innova matching');

  try {
    // Import the enhanced matching function
    const { runEnhancedMatching } = await import('./enhancedInnovaMatching.js');

    // Run the enhanced matching
    const result = await runEnhancedMatching();

    if (result.success) {
      console.log(`[adminServer] Enhanced matching completed: ${result.totalProcessed} OSLs processed`);
      console.log(`[adminServer] Perfect: ${result.perfectMatches}, Good: ${result.goodMatches}, Partial: ${result.partialMatches}, Poor: ${result.poorMatches}`);

      return res.json({
        success: true,
        message: 'Enhanced matching completed successfully',
        totalProcessed: result.totalProcessed,
        perfectMatches: result.perfectMatches,
        goodMatches: result.goodMatches,
        partialMatches: result.partialMatches,
        poorMatches: result.poorMatches,
        enhancedResults: result.enhancedResults
      });
    } else {
      console.error(`[adminServer] Enhanced matching failed: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception in enhanced matching: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Match verification endpoints
app.post('/api/update-match-verification', async (req, res) => {
  console.log('[adminServer] Updating match verification');

  try {
    const { oslId, confirmed } = req.body;

    if (!oslId || typeof confirmed !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and confirmed'
      });
    }

    if (confirmed) {
      // Confirm the match - set verification fields
      const { error } = await supabase
        .from('t_order_sheet_lines')
        .update({
          vendor_match_verified: true,
          vendor_match_verified_at: new Date().toISOString(),
          vendor_match_verified_by: 'admin_interface'
        })
        .eq('id', oslId)
        .eq('vendor_id', 2); // Safety: Only update Innova records

      if (error) {
        console.error(`[adminServer] Error confirming match for OSL ${oslId}:`, error);
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }

      console.log(`[adminServer] Confirmed match for OSL ${oslId}`);
    } else {
      // Reject the match - clear vendor_internal_id and set verification
      const { error } = await supabase
        .from('t_order_sheet_lines')
        .update({
          vendor_internal_id: null,
          vendor_match_verified: false,
          vendor_match_verified_at: new Date().toISOString(),
          vendor_match_verified_by: 'admin_interface'
        })
        .eq('id', oslId)
        .eq('vendor_id', 2); // Safety: Only update Innova records

      if (error) {
        console.error(`[adminServer] Error rejecting match for OSL ${oslId}:`, error);
        return res.status(500).json({
          success: false,
          error: error.message
        });
      }

      console.log(`[adminServer] Rejected match for OSL ${oslId}`);
    }

    return res.json({
      success: true,
      message: confirmed ? 'Match confirmed' : 'Match rejected'
    });
  } catch (err) {
    console.error(`[adminServer] Exception updating match verification: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/bulk-update-match-verification', async (req, res) => {
  console.log('[adminServer] Bulk updating match verification');

  try {
    const { oslIds, confirmed } = req.body;

    if (!Array.isArray(oslIds) || oslIds.length === 0 || typeof confirmed !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslIds (array) and confirmed (boolean)'
      });
    }

    console.log(`[adminServer] Processing ${oslIds.length} OSLs, confirmed: ${confirmed}`);

    let updateData;
    if (confirmed) {
      // Confirm matches
      updateData = {
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'admin_interface'
      };
    } else {
      // Reject matches
      updateData = {
        vendor_internal_id: null,
        vendor_match_verified: false,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'admin_interface'
      };
    }

    const { error } = await supabase
      .from('t_order_sheet_lines')
      .update(updateData)
      .in('id', oslIds)
      .eq('vendor_id', 2); // Safety: Only update Innova records

    if (error) {
      console.error(`[adminServer] Error bulk updating matches:`, error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    console.log(`[adminServer] Successfully bulk updated ${oslIds.length} matches`);

    return res.json({
      success: true,
      message: `Bulk ${confirmed ? 'confirmed' : 'rejected'} ${oslIds.length} matches`,
      updated: oslIds.length
    });
  } catch (err) {
    console.error(`[adminServer] Exception bulk updating match verification: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Enhanced validation report endpoint
app.get('/api/enhanced-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing enhanced validation report');

  try {
    // Import the function to get enhanced validation results
    const { getEnhancedValidationResults } = await import('./enhancedInnovaMatching.js');

    // Get the stored enhanced validation results
    const validationResults = getEnhancedValidationResults();

    if (!validationResults) {
      return res.json({
        success: false,
        error: 'No enhanced validation results found. Please run Enhanced Matching first.'
      });
    }

    console.log(`[adminServer] Returning ${validationResults.length} enhanced validation results`);

    return res.json({
      success: true,
      reportData: validationResults // Return all results for enhanced view
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing enhanced validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 Innova matching endpoint
app.post('/api/phase2-innova-matching', async (req, res) => {
  console.log('[adminServer] Running Phase 2 Innova matching');

  try {
    const { skipCount = 0, batchSize = 300, maxCandidates = 2, minConfidence = 60 } = req.body;
    console.log(`[adminServer] Phase 2 matching with skip: ${skipCount}, batch: ${batchSize}, max: ${maxCandidates}, min: ${minConfidence}%`);

    // Import and run the Phase 2 matching function
    const { runPhase2Matching } = await import('./phase2InnovaMatching.js');

    const result = await runPhase2Matching(skipCount, batchSize, maxCandidates, minConfidence);

    if (result.success) {
      res.json({
        success: true,
        message: `Phase 2 matching completed successfully`,
        stats: result.stats,
        processed: result.processed,
        matches: result.matches
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception in Phase 2 Innova matching: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 validation report endpoint
app.get('/api/phase2-validation-report', async (req, res) => {
  console.log('[adminServer] Viewing Phase 2 validation report');

  try {
    // Import the function to get Phase 2 results
    const { getPhase2Results } = await import('./phase2InnovaMatching.js');

    // Get the stored Phase 2 results
    const validationResults = getPhase2Results();

    if (!validationResults) {
      return res.json({
        success: false,
        error: 'No Phase 2 results found. Please run Phase 2 Matching first.'
      });
    }

    console.log(`[adminServer] Returning ${validationResults.length} Phase 2 validation results`);

    return res.json({
      success: true,
      reportData: validationResults // Return all results for Phase 2 view
    });
  } catch (err) {
    console.error(`[adminServer] Exception viewing Phase 2 validation report: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Create vendor connection endpoint (for Phase 2)
app.post('/api/create-vendor-connection', async (req, res) => {
  console.log('[adminServer] Creating vendor connection');

  try {
    const { oslId, vendorInternalId } = req.body;

    if (!oslId || !vendorInternalId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and vendorInternalId'
      });
    }

    // Update the OSL record to set vendor_internal_id
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        vendor_internal_id: vendorInternalId,
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found or not an Innova record'
      });
    }

    console.log(`[adminServer] Created connection: OSL ${oslId} → Innova ${vendorInternalId}`);

    res.json({
      success: true,
      message: `Connection created successfully`,
      oslId: oslId,
      vendorInternalId: vendorInternalId
    });
  } catch (err) {
    console.error(`[adminServer] Exception creating vendor connection: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Reject Phase 2 match endpoint
app.post('/api/reject-phase2-match', async (req, res) => {
  console.log('[adminServer] Rejecting Phase 2 match');

  try {
    const { oslId, innovaInternalId } = req.body;

    if (!oslId || !innovaInternalId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId and innovaInternalId'
      });
    }

    // Note: The t_vendor_match_rejections table should be created manually using:
    // create_vendor_match_rejections_table.sql
    // This ensures proper permissions and structure

    // Insert the rejection record
    const { data, error } = await supabase
      .from('t_vendor_match_rejections')
      .upsert({
        osl_id: oslId,
        vendor_id: 2, // Innova
        vendor_internal_id: innovaInternalId,
        rejected_at: new Date().toISOString(),
        rejected_by: 'admin_interface'
      })
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`[adminServer] Rejected match: OSL ${oslId} ↔ Innova ${innovaInternalId}`);

    res.json({
      success: true,
      message: `Match rejection recorded`,
      oslId: oslId,
      innovaInternalId: innovaInternalId
    });
  } catch (err) {
    console.error(`[adminServer] Exception rejecting Phase 2 match: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Phase 2 bulk connect endpoint (creates actual vendor connections)
app.post('/api/phase2-bulk-connect', async (req, res) => {
  console.log('[adminServer] Phase 2 bulk connecting matches');

  try {
    const { connections } = req.body;

    if (!Array.isArray(connections) || connections.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: connections (array of {oslId, vendorInternalId})'
      });
    }

    console.log(`[adminServer] Processing ${connections.length} Phase 2 connections`);

    let successCount = 0;
    let errors = [];

    // Process each connection
    for (const connection of connections) {
      const { oslId, vendorInternalId } = connection;

      if (!oslId || !vendorInternalId) {
        errors.push(`Invalid connection: oslId=${oslId}, vendorInternalId=${vendorInternalId}`);
        continue;
      }

      try {
        const { error } = await supabase
          .from('t_order_sheet_lines')
          .update({
            vendor_internal_id: vendorInternalId,
            vendor_match_verified: true,
            vendor_match_verified_at: new Date().toISOString(),
            vendor_match_verified_by: 'phase2_bulk_connect'
          })
          .eq('id', oslId)
          .eq('vendor_id', 2); // Only Innova records

        if (error) {
          errors.push(`OSL ${oslId}: ${error.message}`);
        } else {
          successCount++;
        }
      } catch (err) {
        errors.push(`OSL ${oslId}: ${err.message}`);
      }
    }

    console.log(`[adminServer] Phase 2 bulk connect completed: ${successCount} success, ${errors.length} errors`);

    return res.json({
      success: true,
      message: `Bulk connected ${successCount} matches`,
      connected: successCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (err) {
    console.error(`[adminServer] Exception in Phase 2 bulk connect: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Update OSL weights to match Innova and create connection
app.post('/api/update-weights-and-connect', async (req, res) => {
  console.log('[adminServer] Updating OSL weights and creating connection');

  try {
    const { oslId, innovaInternalId, newMinWeight, newMaxWeight, originalWeights, innovaWeights } = req.body;

    if (!oslId || !innovaInternalId || !newMinWeight || !newMaxWeight) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: oslId, innovaInternalId, newMinWeight, newMaxWeight'
      });
    }

    console.log(`[adminServer] OSL ${oslId}: Updating weights from ${originalWeights} to ${newMinWeight}-${newMaxWeight} and connecting to Innova ${innovaInternalId}`);

    // Update the OSL record with new weights and vendor connection
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        min_weight: newMinWeight,
        max_weight: newMaxWeight,
        vendor_internal_id: innovaInternalId,
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'update_weights_and_connect',
        updated_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found'
      });
    }

    console.log(`[adminServer] Successfully updated OSL ${oslId} weights and created connection`);

    res.json({
      success: true,
      message: `OSL ${oslId} weights updated to ${newMinWeight}-${newMaxWeight} and connected to Innova ${innovaInternalId}`,
      oslId: oslId,
      innovaInternalId: innovaInternalId,
      oldWeights: originalWeights,
      newWeights: `${newMinWeight}-${newMaxWeight}`
    });
  } catch (err) {
    console.error(`[adminServer] Exception updating weights and connecting: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Clear OSL from Phase 2 matching (mark as verified without vendor connection)
app.post('/api/clear-from-phase2', async (req, res) => {
  console.log('[adminServer] Clearing OSL from Phase 2 matching');

  try {
    const { oslId } = req.body;

    if (!oslId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: oslId'
      });
    }

    console.log(`[adminServer] Clearing OSL ${oslId} from Phase 2 (marking as verified without connection)`);

    // Mark the OSL as verified but leave vendor_internal_id as null
    // This removes it from Phase 2 matching without creating a vendor connection
    const { data, error } = await supabase
      .from('t_order_sheet_lines')
      .update({
        vendor_match_verified: true,
        vendor_match_verified_at: new Date().toISOString(),
        vendor_match_verified_by: 'clear_from_phase2',
        updated_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .eq('vendor_id', 2) // Only Innova records
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'OSL record not found'
      });
    }

    console.log(`[adminServer] Successfully cleared OSL ${oslId} from Phase 2 matching`);

    res.json({
      success: true,
      message: `OSL ${oslId} cleared from Phase 2 matching (marked as verified without vendor connection)`,
      oslId: oslId
    });
  } catch (err) {
    console.error(`[adminServer] Exception clearing OSL from Phase 2: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Mark MPS as inactive endpoint
app.post('/api/mark-mps-inactive', async (req, res) => {
  console.log('[adminServer] Marking MPS as inactive');

  try {
    const { mpsId } = req.body;

    if (!mpsId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: mpsId'
      });
    }

    // Update the MPS record to set active = false
    const { data, error } = await supabase
      .from('t_mps')
      .update({
        active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', mpsId)
      .select();

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'MPS record not found'
      });
    }

    console.log(`[adminServer] Marked MPS ${mpsId} as inactive`);

    res.json({
      success: true,
      message: `MPS ${mpsId} marked as inactive`,
      mpsId: mpsId
    });
  } catch (err) {
    console.error(`[adminServer] Exception marking MPS as inactive: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// B2F API endpoints
app.get('/api/b2f/count', async (req, res) => {
  console.log('[adminServer] Getting B2F record count');

  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized'
    });
  }

  try {
    // Use the updated view that includes disc ID
    const { count, error } = await supabase
      .from('v_b2f_pick_slim')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('[adminServer] Error counting B2F records:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    res.json({
      success: true,
      count: count || 0
    });

  } catch (err) {
    console.error('[adminServer] Exception getting B2F count:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});



app.post('/api/b2f/auto-select', async (req, res) => {
  console.log('[adminServer] Auto-selecting B2F discs');

  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized'
    });
  }

  try {
    const { maxOsls = 5 } = req.body; // Default to 5 OSLs for gradual rollout
    console.log(`[adminServer] Auto-selecting B2F discs for up to ${maxOsls} OSLs`);

    // Get today's date in MM-DD format
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const b2fLocation = `B2F ${month}-${day}`;

    // Get all available B2F candidates grouped by OSL
    const { data: candidates, error: candidatesError } = await supabase
      .from('v_b2f_pick_slim')
      .select('*')
      .order('osl_id')
      .order('disc_grade', { ascending: false }) // Higher grades first
      .order('id'); // Older discs first for same grade

    if (candidatesError) {
      console.error('[adminServer] Error getting B2F candidates:', candidatesError);
      return res.status(500).json({
        success: false,
        error: candidatesError.message
      });
    }

    if (!candidates || candidates.length === 0) {
      return res.json({
        success: true,
        message: 'No B2F candidates found',
        selectedDiscs: [],
        summary: { totalOsls: 0, totalDiscs: 0 }
      });
    }

    // Group candidates by OSL ID
    const candidatesByOsl = {};
    candidates.forEach(disc => {
      if (!candidatesByOsl[disc.osl_id]) {
        candidatesByOsl[disc.osl_id] = [];
      }
      candidatesByOsl[disc.osl_id].push(disc);
    });

    console.log(`[adminServer] Found ${Object.keys(candidatesByOsl).length} OSLs with available discs`);

    // Get MPS data for all OSLs to check release dates
    const oslIds = Object.keys(candidatesByOsl).map(id => parseInt(id));
    console.log(`[adminServer] Fetching MPS data for ${oslIds.length} OSLs...`);

    const { data: mpsData, error: mpsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`
        id,
        mps_id,
        t_mps!inner(
          id,
          release_date_online,
          created_at
        )
      `)
      .in('id', oslIds);

    if (mpsError) {
      console.error('[adminServer] Error fetching MPS data:', mpsError);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch MPS release date data'
      });
    }

    // Create a map of OSL ID to MPS data for quick lookup
    const mpsDataMap = {};
    mpsData.forEach(osl => {
      mpsDataMap[osl.id] = osl.t_mps;
    });

    const selectedDiscs = [];
    const processedOsls = [];
    let oslCount = 0;

    // Process each OSL (limited by maxOsls parameter)
    for (const [oslId, discs] of Object.entries(candidatesByOsl)) {
      if (oslCount >= maxOsls) break;

      const salesQuantity = discs[0].osl_sold_last_30_dz_plus_retail || 0;
      const mpsInfo = mpsDataMap[parseInt(oslId)];

      // Calculate quantity needed based on sales and release date logic
      let quantityNeeded = salesQuantity === 0 ? 1 : salesQuantity;

      // Get current stock levels for this OSL
      const { data: stockData, error: stockError } = await supabase
        .from('v_stats_by_osl')
        .select('discs_in_stock_fs, discs_in_stock_b2f')
        .eq('id', parseInt(oslId))
        .single();

      const currentFrontStock = stockData?.discs_in_stock_fs || 0;
      const currentB2FStock = stockData?.discs_in_stock_b2f || 0;
      const totalCurrentStock = currentFrontStock + currentB2FStock;

      // Check for new release logic
      if (mpsInfo) {
        const now = new Date();
        const oneMonthAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

        let isNewRelease = false;
        let releaseType = 'standard';
        let minStockTarget = 0;

        if (mpsInfo.release_date_online) {
          const releaseDate = new Date(mpsInfo.release_date_online);
          // If release date is within past month or in the future
          if (releaseDate >= oneMonthAgo) {
            isNewRelease = true;
            releaseType = 'online_release';
            minStockTarget = 5; // Min 5 total for new releases
          }
        } else if (mpsInfo.created_at) {
          const createdDate = new Date(mpsInfo.created_at);
          // If no release date but created within last month
          if (createdDate >= oneMonthAgo) {
            isNewRelease = true;
            releaseType = 'new_mps';
            minStockTarget = 3; // Min 3 total for new MPS
          }
        }

        // For new releases, ensure we have enough total stock (front + B2F)
        if (isNewRelease && minStockTarget > 0) {
          const stockNeeded = Math.max(0, minStockTarget - totalCurrentStock);
          quantityNeeded = Math.max(quantityNeeded, stockNeeded);
        }

        console.log(`[adminServer] Processing OSL ${oslId} (${releaseType}): ${salesQuantity} sales, ${totalCurrentStock} current stock (${currentFrontStock} FS + ${currentB2FStock} B2F), ${quantityNeeded} needed (new release: ${isNewRelease}, target: ${minStockTarget}) from ${discs.length} available`);
      } else {
        console.log(`[adminServer] Processing OSL ${oslId}: ${salesQuantity} sales, ${totalCurrentStock} current stock, ${quantityNeeded} needed (no MPS data) from ${discs.length} available`);
      }

      // Separate discs by grade (handle null grades as ungraded)
      const gradedDiscs = discs.filter(d => d.disc_grade && d.disc_grade > 0);
      const ungradedDiscs = discs.filter(d => !d.disc_grade || d.disc_grade === 0);

      console.log(`[adminServer] OSL ${oslId}: ${gradedDiscs.length} graded, ${ungradedDiscs.length} ungraded discs`);

      let selectedForThisOsl = [];

      // First, try to select from graded discs
      if (gradedDiscs.length > 0) {
        selectedForThisOsl = selectDiscsWithVariety(gradedDiscs, quantityNeeded);
      }

      // If we still need more discs, select from ungraded (oldest first)
      const stillNeeded = quantityNeeded - selectedForThisOsl.length;
      if (stillNeeded > 0 && ungradedDiscs.length > 0) {
        const additionalSelected = selectDiscsWithVariety(ungradedDiscs, stillNeeded);
        selectedForThisOsl = selectedForThisOsl.concat(additionalSelected);
      }

      if (selectedForThisOsl.length > 0) {
        selectedDiscs.push(...selectedForThisOsl);
        processedOsls.push({
          oslId: oslId,
          osl: discs[0].osl,
          needed: quantityNeeded,
          selected: selectedForThisOsl.length,
          discs: selectedForThisOsl.map(d => ({ id: d.id, disc: d.disc, grade: d.disc_grade }))
        });
        oslCount++;
      }
    }

    // Helper function to select discs with color variety
    function selectDiscsWithVariety(discs, quantity) {
      if (quantity >= discs.length) {
        return discs; // Take all available
      }

      if (quantity === 1) {
        return [discs[0]]; // Take the first (highest grade or oldest)
      }

      // For multiple discs, try to get color variety
      const selected = [];
      const usedColors = new Set();

      // First pass: try to get different colors
      for (const disc of discs) {
        if (selected.length >= quantity) break;

        // Extract color from disc name (rough heuristic)
        const colorMatch = disc.disc.match(/\b(Red|Blue|Green|Yellow|Orange|Purple|Pink|White|Black|Clear|Glow)\b/i);
        const color = colorMatch ? colorMatch[0].toLowerCase() : 'unknown';

        if (!usedColors.has(color)) {
          selected.push(disc);
          usedColors.add(color);
        }
      }

      // Second pass: fill remaining slots if needed
      for (const disc of discs) {
        if (selected.length >= quantity) break;
        if (!selected.includes(disc)) {
          selected.push(disc);
        }
      }

      return selected;
    }

    // Update selected discs to B2F location
    let updateCount = 0;
    const updateErrors = [];

    for (const disc of selectedDiscs) {
      try {
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ location: b2fLocation })
          .eq('id', disc.id);

        if (updateError) {
          console.error(`[adminServer] Error updating disc ${disc.id}:`, updateError);
          updateErrors.push(`Disc ${disc.id}: ${updateError.message}`);
        } else {
          updateCount++;
        }
      } catch (err) {
        console.error(`[adminServer] Exception updating disc ${disc.id}:`, err);
        updateErrors.push(`Disc ${disc.id}: ${err.message}`);
      }
    }

    const summary = {
      totalOsls: processedOsls.length,
      totalDiscs: selectedDiscs.length,
      successfulUpdates: updateCount,
      errors: updateErrors.length
    };

    console.log(`[adminServer] Auto-selection complete: ${summary.totalOsls} OSLs, ${summary.successfulUpdates}/${summary.totalDiscs} discs updated`);

    res.json({
      success: true,
      message: `Auto-selected ${summary.successfulUpdates} discs for ${summary.totalOsls} OSLs`,
      selectedDiscs: processedOsls,
      summary: summary,
      errors: updateErrors.length > 0 ? updateErrors : undefined
    });

  } catch (err) {
    console.error('[adminServer] Exception in auto-select B2F:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.post('/api/b2f/generate-pdf', async (req, res) => {
  console.log('[adminServer] Generating B2F PDF list');

  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized'
    });
  }

  try {
    // Get today's date in MM-DD format
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const b2fLocation = `B2F ${month}-${day}`;

    // Query discs with today's B2F location
    const { data, error } = await supabase
      .from('t_discs')
      .select(`
        id,
        g_pull,
        g_title,
        location,
        t_order_sheet_lines!inner(g_code)
      `)
      .eq('location', b2fLocation)
      .order('id');

    if (error) {
      console.error('[adminServer] Error getting B2F discs for PDF:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Import PDF generation library
    const { PDFDocument, rgb, StandardFonts } = await import('pdf-lib');

    // Create PDF document
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Add a page
    const page = pdfDoc.addPage([612, 792]); // Letter size
    const { width, height } = page.getSize();

    // Title
    const title = `B2F List - ${month}/${day}/${today.getFullYear()}`;
    const titleSize = 16;
    const titleWidth = boldFont.widthOfTextAtSize(title, titleSize);
    page.drawText(title, {
      x: (width - titleWidth) / 2,
      y: height - 50,
      size: titleSize,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    // Headers
    const headerY = height - 100;
    const headerSize = 12;
    page.drawText('Disc ID', { x: 50, y: headerY, size: headerSize, font: boldFont });
    page.drawText('G-Code', { x: 150, y: headerY, size: headerSize, font: boldFont });
    page.drawText('Title', { x: 250, y: headerY, size: headerSize, font: boldFont });

    // Draw header line
    page.drawLine({
      start: { x: 50, y: headerY - 5 },
      end: { x: width - 50, y: headerY - 5 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // List discs
    let currentY = headerY - 30;
    const lineHeight = 20;
    const textSize = 10;
    let currentPage = page; // Keep track of current page

    if (data && data.length > 0) {
      data.forEach((disc, index) => {
        // Check if we need a new page
        if (currentY < 50) {
          currentPage = pdfDoc.addPage([612, 792]);

          // Add headers to new page
          const newHeaderY = height - 100;
          const headerSize = 12;
          currentPage.drawText('Disc ID', { x: 50, y: newHeaderY, size: headerSize, font: boldFont });
          currentPage.drawText('G-Code', { x: 150, y: newHeaderY, size: headerSize, font: boldFont });
          currentPage.drawText('Title', { x: 250, y: newHeaderY, size: headerSize, font: boldFont });

          // Draw header line
          currentPage.drawLine({
            start: { x: 50, y: newHeaderY - 5 },
            end: { x: width - 50, y: newHeaderY - 5 },
            thickness: 1,
            color: rgb(0, 0, 0),
          });

          currentY = newHeaderY - 30;
        }

        const discId = disc.g_pull || `D${disc.id}`;
        const gCode = disc.t_order_sheet_lines?.g_code || 'N/A';
        const title = disc.g_title || 'N/A';

        currentPage.drawText(discId, { x: 50, y: currentY, size: textSize, font: font });
        currentPage.drawText(gCode, { x: 150, y: currentY, size: textSize, font: font });
        currentPage.drawText(title.length > 25 ? title.substring(0, 25) + '...' : title, { x: 250, y: currentY, size: textSize, font: font });

        currentY -= lineHeight;
      });
    } else {
      currentPage.drawText('No discs found for today\'s B2F location.', {
        x: 50,
        y: currentY,
        size: textSize,
        font: font,
        color: rgb(0.5, 0.5, 0.5),
      });
    }

    // Footer on last page
    const footerText = `Generated on ${today.toLocaleDateString()} at ${today.toLocaleTimeString()}`;
    const footerSize = 8;
    currentPage.drawText(footerText, {
      x: 50,
      y: 30,
      size: footerSize,
      font: font,
      color: rgb(0.5, 0.5, 0.5),
    });

    // Generate PDF bytes
    const pdfBytes = await pdfDoc.save();

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="B2F_List_${month}-${day}.pdf"`);
    res.setHeader('Content-Length', pdfBytes.length);

    // Send PDF
    res.send(Buffer.from(pdfBytes));

    console.log(`[adminServer] Generated B2F PDF with ${data?.length || 0} discs`);

  } catch (err) {
    console.error('[adminServer] Exception generating B2F PDF:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Simple text file endpoint for B2F list
app.post('/api/b2f/generate-text', async (req, res) => {
  console.log('[adminServer] Generating B2F text list');

  if (!supabase) {
    return res.status(500).json({
      success: false,
      error: 'Supabase client not initialized'
    });
  }

  try {
    // Get today's B2F location
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const todayLocation = `B2F ${month}-${day}`;

    console.log(`[adminServer] Looking for B2F discs with location: ${todayLocation}`);

    // Query for today's B2F discs, get g_pull and tag_printed_at fields
    const { data, error } = await supabase
      .from('t_discs')
      .select('g_pull, tag_printed_at')
      .eq('location', todayLocation)
      .not('g_pull', 'is', null)
      .order('g_pull');

    if (error) {
      console.error('[adminServer] Error fetching B2F data:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    console.log(`[adminServer] Found ${data?.length || 0} B2F discs for ${todayLocation}`);

    // Create simple text content
    let textContent = `B2F List - ${month}/${day}/${today.getFullYear()}\n`;
    textContent += `Generated: ${today.toLocaleString()}\n`;
    textContent += `Location: ${todayLocation}\n`;
    textContent += `Total Discs: ${data?.length || 0}\n\n`;
    textContent += `Disc ID\t\tTag Printed At\n`;
    textContent += `=======\t\t==============\n`;

    if (data && data.length > 0) {
      data.forEach(disc => {
        const tagPrintedAt = disc.tag_printed_at
          ? new Date(disc.tag_printed_at).toLocaleString()
          : 'Not printed';
        textContent += `${disc.g_pull}\t\t${tagPrintedAt}\n`;
      });
    } else {
      textContent += 'No discs found for today\'s B2F location.\n';
    }

    // Set response headers for text file download
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="B2F_List_${month}-${day}.txt"`);
    res.setHeader('Content-Length', Buffer.byteLength(textContent, 'utf8'));

    // Send text file
    res.send(textContent);

    console.log(`[adminServer] Generated B2F text file with ${data?.length || 0} discs`);

  } catch (err) {
    console.error('[adminServer] Exception generating B2F text file:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// API endpoint to find duplicate Shopify products
app.post('/api/shopify/find-duplicates', async (req, res) => {
  console.log('[adminServer] Finding duplicate Shopify products');

  try {
    // Import the findDuplicateShopifyProducts module
    const findDuplicates = (await import('./findDuplicateShopifyProducts.js')).default;

    // Run the find duplicates function
    const result = await findDuplicates();

    if (result.success) {
      res.json({
        success: true,
        duplicates: result.duplicates,
        totalFound: result.totalFound,
        message: `Found ${result.totalFound} duplicate products`
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        duplicates: []
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception finding duplicate Shopify products: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      duplicates: []
    });
  }
});

// API endpoint to delete duplicate Shopify products
app.post('/api/shopify/delete-duplicates', async (req, res) => {
  console.log('[adminServer] Deleting duplicate Shopify products');

  try {
    const { productIds } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Product IDs array is required'
      });
    }

    console.log(`[adminServer] Deleting ${productIds.length} products: ${productIds.join(', ')}`);

    // Import the deleteDuplicateShopifyProducts module
    const deleteProducts = (await import('./deleteDuplicateShopifyProducts.js')).default;

    // Run the delete function
    const result = await deleteProducts(productIds);

    if (result.success) {
      res.json({
        success: true,
        deletedCount: result.deletedCount,
        errorCount: result.errorCount,
        errors: result.errors,
        message: `Successfully deleted ${result.deletedCount} out of ${result.totalProcessed} products`
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error,
        deletedCount: 0,
        errors: result.errors || []
      });
    }
  } catch (err) {
    console.error(`[adminServer] Exception deleting duplicate Shopify products: ${err.message}`);
    res.status(500).json({
      success: false,
      error: err.message,
      deletedCount: 0,
      errors: [err.message]
    });
  }
});

// API endpoint to get smart collection count
app.get('/api/shopify/smart-collections/count', async (req, res) => {
  console.log('[adminServer] Getting smart collection count from Shopify');

  try {
    const response = await fetch(`https://${process.env.SHOPIFY_STORE_DOMAIN}/admin/api/2023-10/smart_collections/count.json`, {
      method: 'GET',
      headers: {
        'X-Shopify-Access-Token': process.env.SHOPIFY_ACCESS_TOKEN,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Shopify API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const count = data.count;

    console.log(`[adminServer] Found ${count} smart collections on Shopify`);

    res.json({
      success: true,
      count: count,
      limit: 5000,
      remaining: 5000 - count,
      atLimit: count >= 5000
    });

  } catch (error) {
    console.error('[adminServer] Error getting smart collection count:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint to preview old MPS collections for cleanup
app.get('/api/mps-collections/preview-cleanup', async (req, res) => {
  console.log('[adminServer] Getting preview of old MPS collections for cleanup');

  try {
    const limit = parseInt(req.query.limit) || 10;
    const collections = await getOldestEmptyMpsCollections(limit);

    res.json({
      success: true,
      collections: collections,
      count: collections.length,
      message: `Found ${collections.length} empty MPS collections ready for deletion`
    });

  } catch (error) {
    console.error('[adminServer] Error getting MPS collections preview:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint to cleanup old MPS collections
app.post('/api/mps-collections/cleanup', async (req, res) => {
  console.log('[adminServer] Starting MPS collections cleanup');

  try {
    const { collections } = req.body;

    if (!collections || !Array.isArray(collections) || collections.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No collections provided for cleanup'
      });
    }

    console.log(`[adminServer] Cleaning up ${collections.length} MPS collections`);
    const results = await cleanupOldMpsCollections(collections);

    res.json({
      success: results.errors.length === 0,
      results: results,
      message: `Cleanup complete: ${results.success.length} deleted, ${results.errors.length} errors`
    });

  } catch (error) {
    console.error('[adminServer] Error during MPS collections cleanup:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint to preview old MPS collections for cleanup
app.get('/api/mps-collections/preview-cleanup', async (req, res) => {
  console.log('[adminServer] Getting preview of old MPS collections for cleanup');

  try {
    const limit = parseInt(req.query.limit) || 10;
    const collections = await getOldestEmptyMpsCollections(limit);

    res.json({
      success: true,
      collections: collections,
      count: collections.length,
      message: `Found ${collections.length} empty MPS collections ready for deletion`
    });

  } catch (error) {
    console.error('[adminServer] Error getting MPS collections preview:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint to cleanup old MPS collections
app.post('/api/mps-collections/cleanup', async (req, res) => {
  console.log('[adminServer] Starting MPS collections cleanup');

  try {
    const { collections } = req.body;

    if (!collections || !Array.isArray(collections) || collections.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No collections provided for cleanup'
      });
    }

    console.log(`[adminServer] Cleaning up ${collections.length} MPS collections`);
    const results = await cleanupOldMpsCollections(collections);

    res.json({
      success: results.errors.length === 0,
      results: results,
      message: `Cleanup complete: ${results.success.length} deleted, ${results.errors.length} errors`
    });

  } catch (error) {
    console.error('[adminServer] Error during MPS collections cleanup:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start server
const PORT = process.env.PORT || 3001; // Always use port 3001 by default
startServer(PORT);
