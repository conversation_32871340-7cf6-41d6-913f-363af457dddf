<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Queue Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-banner {
            background-color: #ff0000;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .start-worker-btn {
            background-color: #2ecc71;
            margin-left: 10px;
        }
        .start-worker-btn:hover {
            background-color: #27ae60;
        }
        .run-once-btn {
            background-color: #f39c12;
            margin-left: 10px;
        }
        .run-once-btn:hover {
            background-color: #d35400;
        }
        .run-daemon-btn {
            background-color: #2ecc71;
            margin-left: 10px;
        }
        .run-daemon-btn:hover {
            background-color: #27ae60;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
            margin-left: 10px;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
            margin-left: 5px;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            top: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 14px;
            line-height: 1.4;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .badge {
            display: inline-block;
            padding: 3px 7px;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 10px;
            background-color: #3498db;
        }
        .readme {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 30px;
        }
        .readme pre {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
        }
        .readme code {
            font-family: monospace;
        }
        .workflow-diagram {
            padding: 20px;
        }
        .workflow-steps {
            counter-reset: step;
            padding-left: 0;
        }
        .workflow-steps li {
            list-style-type: none;
            position: relative;
            margin-bottom: 30px;
            padding: 15px 20px 15px 70px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .workflow-steps li:before {
            counter-increment: step;
            content: counter(step);
            position: absolute;
            left: 20px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
        }
        .workflow-steps li strong {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 18px;
        }
        .workflow-steps li p {
            margin-top: 0;
        }
        .workflow-steps li code {
            background-color: #e9ecef;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .task-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 10px;
        }
        .task-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .task-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .task-table .task-name {
            font-weight: bold;
            width: 60%;
        }
        .task-table .count {
            text-align: center;
            font-weight: bold;
        }
        .task-table .count-zero {
            color: #95a5a6;
        }
        .task-table .count-nonzero {
            color: #2980b9;
        }
        .task-table .count-success {
            color: #27ae60;
        }
        .task-table .count-error {
            color: #c0392b;
        }
        .task-table .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-running {
            background-color: #2ecc71;
        }
        .status-stopped {
            background-color: #e74c3c;
        }
        #workerStatus, #workerTabStatus {
            font-weight: bold;
            margin-left: 10px;
        }
        .worker-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .worker-mode-section {
            flex: 1;
        }
        .worker-buttons {
            display: flex;
            gap: 10px;
        }
        .worker-status-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .run-worker-btn {
            background-color: #2ecc71;
        }
        .run-worker-btn:hover {
            background-color: #27ae60;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .success-message {
            color: #2ecc71;
            font-weight: bold;
        }
        .failure-message {
            color: #e74c3c;
            font-weight: bold;
        }
        .console-log {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            color: #f0f0f0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 14px;
        }
        .console-log .log-entry {
            margin-bottom: 4px;
            border-bottom: 1px solid #333;
            padding-bottom: 4px;
        }
        .console-log .timestamp {
            color: #888;
            margin-right: 8px;
        }
        .console-log .task-id {
            color: #569cd6;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .task-type {
            color: #4ec9b0;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .success {
            color: #6a9955;
        }
        .console-log .error {
            color: #f14c4c;
        }
        .console-log .warning {
            color: #dcdcaa;
        }
        .console-log .info {
            color: #9cdcfe;
        }
        .console-log .processing {
            color: #ce9178;
        }
        .console-log .completed {
            color: #6a9955;
            font-weight: bold;
        }
        .console-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }

        /* Task Types Reference Styles */
        .task-types-reference {
            padding: 20px;
        }
        .task-category {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .task-category h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .task-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .task-item strong {
            color: #e74c3c;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            display: block;
            margin-bottom: 8px;
        }
        .task-item p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.4;
        }
        .task-item ul {
            margin: 8px 0;
            padding-left: 20px;
        }
        .task-item li {
            margin: 3px 0;
            font-size: 12px;
        }
        .task-item code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 11px;
            color: #e74c3c;
        }
        .timing-info, .triggers-summary {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .timing-info h4, .triggers-summary h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }
        .timing-info ul, .triggers-summary ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .timing-info li, .triggers-summary li {
            margin: 5px 0;
            font-size: 14px;
        }
        .clear-console-btn {
            background-color: #7f8c8d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .clear-console-btn:hover {
            background-color: #95a5a6;
        }
        .auto-scroll-label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #f5f5f5;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 5px 0;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* Enhanced Informed Tab Styles */
        .workflow-section {
            margin-bottom: 30px;
        }

        .step-group {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .step-group h3 {
            margin-top: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .step-btn {
            min-width: 180px;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .step-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .workflow-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .workflow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .info-box {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .success-highlight {
            background: #e8f5e8;
            border-left-color: #27ae60;
            color: #27ae60;
        }

        .warning-highlight {
            background: #fff3cd;
            border-left-color: #f39c12;
            color: #856404;
        }

        .enhanced-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }

        .step-output {
            background: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px;
            margin-top: 10px;
            border-radius: 0 4px 4px 0;
        }

        .output-section {
            margin-top: 20px;
        }

        .card-header.gradient-blue {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .card-header.gradient-green {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .card-header.gradient-orange {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
        }

        .card-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="test-banner">
        TEST BANNER - This is a test to verify the page is loading correctly
    </div>
    <header>
        <h1>Task Queue Admin Dashboard</h1>
        <p>Manage and monitor your task queue system</p>
    </header>

    <div class="tabs">
        <div class="tab active" data-tab="dashboard">Dashboard</div>
        <div class="tab" data-tab="tasks">Tasks</div>
        <div class="tab" data-tab="reconcile">Reconcile</div>
        <div class="tab" data-tab="worker">Worker</div>
        <div class="tab" data-tab="flow">Workflow</div>
        <div class="tab" data-tab="informed">Informed</div>
        <div class="tab" data-tab="discraft">Discraft</div>
        <div class="tab" data-tab="innova">Innova</div>
        <div class="tab" data-tab="b2f">B2F</div>
        <div class="tab" data-tab="shopify-dupes">Dupe Shopify Products</div>
        <div class="tab" data-tab="documentation">Documentation</div>
        <div class="tab" data-tab="amazon">Amazon FBA</div>
        <div class="tab" data-tab="todo">ToDo</div>
    </div>

    <div id="dashboard" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>System Overview</h2>
            </div>
            <div>
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerStatus">Stopped</span>
                    <button id="refreshStatus">Refresh</button>
                    <button id="dashboardRunWorkerOnce" class="run-once-btn">Run Worker Once</button>
                    <button id="dashboardRunWorkerDaemon" class="run-daemon-btn">Run Worker as Daemon</button>
                </p>
                <p><strong>Pending Tasks:</strong> <span id="pendingTasksCount">Loading...</span></p>
                <p><strong>Future Scheduled Tasks:</strong> <span id="futureTasksCount">Loading...</span></p>
                <p><strong>Last Run:</strong> <span id="lastRunTime">Unknown</span></p>
            </div>

            <div class="card-header">
                <h3>Task Queue Status by Workflow Step</h3>
            </div>
            <div id="taskWorkflowTable">
                <table class="task-table">
                    <thead>
                        <tr>
                            <th>Task Type</th>
                            <th>Pending</th>
                            <th>Future</th>
                            <th>Completed (30m)</th>
                            <th>Errors (30m)</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                        <tr>
                            <td colspan="5" class="loading-message">Loading task statistics...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="card-header">
                <h3>Live Console Log</h3>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Real-time output from the worker process.
                    </span>
                </div>
            </div>
            <div class="console-controls">
                <div>
                    <button id="clearConsoleBtn" class="clear-console-btn">Clear Console</button>
                    <select id="logFilterSelect" title="Filter log entries">
                        <option value="all">All Messages</option>
                        <option value="error">Errors Only</option>
                        <option value="success">Success Only</option>
                        <option value="processing">Processing Only</option>
                    </select>
                    <input type="text" id="taskTypeFilter" placeholder="Filter by task type..." style="width: 180px;">
                </div>
                <div>
                    <label class="auto-scroll-label">
                        <input type="checkbox" id="autoScrollCheckbox" checked>
                        Auto-scroll
                    </label>
                    <label class="auto-scroll-label" style="margin-left: 10px;">
                        <input type="checkbox" id="showTimestampsCheckbox" checked>
                        Show Timestamps
                    </label>
                </div>
            </div>
            <div id="dashboardConsoleLog" class="output console-log"></div>
        </div>
    </div>

    <div id="tasks" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Enqueue Image Verification Task</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This tool creates a new task in the t_task_queue table to verify an image.
                        The task will be processed by the worker when its scheduled time is reached.
                    </span>
                </div>
            </div>
            <form id="enqueueForm">
                <div class="form-group">
                    <label for="imageId">Image ID <span class="badge">Required</span></label>
                    <input type="number" id="imageId" name="imageId" required placeholder="Enter the t_images.id value">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            The ID of the image record in the t_images table that you want to verify.
                            This will be used in the task payload as {"id": 123456}.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="delayMinutes">Delay (minutes)</label>
                    <input type="number" id="delayMinutes" name="delayMinutes" value="0" min="0" placeholder="0 = immediate execution">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Number of minutes to delay task execution. Use 0 for immediate execution.
                            Tasks will only be processed when their scheduled time is reached.
                        </span>
                    </div>
                </div>
                <button type="submit">Enqueue Task</button>
            </form>
            <div id="enqueueOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>View Pending Tasks</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        View and manage tasks currently in the queue.
                    </span>
                </div>
            </div>
            <button id="viewTasksBtn">Refresh Task List</button>
            <div id="tasksOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Import Discs from Google Sheets</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Import disc data from a Google Sheets document into the t_discs table with validation and foreign key mapping.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Make sure your Google Sheet is publicly viewable</li>
                    <li>The sheet should have columns: shipment_id, mps_id, color_modifier, color, weight_mfg, weight_scale, grade, description, location, notes, new_id</li>
                    <li>Column mapping: weight_scale → weight, weight_mfg → weight_mfg</li>
                    <li>The 'new_id' column will be populated with the new t_discs.id values after import</li>
                    <li><strong>Most fields are required - any blank required field will mark the row as invalid</strong></li>
                </ol>
                <p><strong>Required Fields:</strong> shipment_id, mps_id, color, weight_mfg, weight_scale, grade, description, location</p>
                <p><strong>Optional Fields:</strong> color_modifier, notes, new_id</p>
                <p><strong>Validation:</strong> shipment_id must exist in t_shipments, mps_id must exist in t_mps, color must match t_colors.color</p>
            </div>
            <form id="importDiscsForm">
                <div class="form-group">
                    <label for="googleSheetsUrl">Google Sheets URL <span class="badge">Required</span></label>
                    <input type="url" id="googleSheetsUrl" name="googleSheetsUrl" required
                           placeholder="https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit#gid=0"
                           style="width: 100%;">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Paste the full Google Sheets URL. The sheet must be publicly viewable for the import to work.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="validateOnly">
                        <input type="checkbox" id="validateOnly" name="validateOnly">
                        Validate Only (don't import)
                    </label>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Check this to only validate the data without importing. Use this to check for errors first.
                        </span>
                    </div>
                </div>
                <button type="submit">Import Discs from Google Sheets</button>
            </form>
            <div id="importDiscsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Delete Wrong Veeqo Records</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Deletes Veeqo product variants with variant titles containing " (D#" through the Veeqo API.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Warning:</strong> This will permanently delete product variants in Veeqo that have variant titles containing " (D#".</p>
                <p>These are typically duplicate records that were created incorrectly and should be removed.</p>
                <p>This action cannot be undone. Please make sure you have a recent backup of your Veeqo data.</p>
            </div>
            <div class="form-group">
                <label for="deleteVeeqoLimit">Number of records to delete:</label>
                <select id="deleteVeeqoLimit" class="form-control">
                    <option value="1">Delete 1 record (test)</option>
                    <option value="10">Delete 10 records</option>
                    <option value="">Delete all records</option>
                </select>
            </div>
            <button id="deleteWrongVeeqoRecordsBtn" class="run-once-btn">Delete Wrong Veeqo Records</button>
            <div id="deleteWrongVeeqoRecordsOutput" class="output" style="display: none;"></div>
        </div>


    </div>

    <div id="reconcile" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSL Stats Into t_inv_osl</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_osl records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_osl_inventory</code> function which updates <code>t_inv_osl</code> records with correct quantity counts from the <code>v_stats_by_osl</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_osl</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileOslStatsBtn" class="run-once-btn">Reconcile OSL Stats to t_inv_osl to Veeqo</button>
            <div id="reconcileOslStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Import RPRO Data</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Imports the latest RPRO DBF file (invdb.dbf) into the imported_table_rpro table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #2ecc71; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process imports the latest RPRO inventory data from the DBF file (<code>R:\Rpro\BRIDGE\invdb.dbf</code>) into the <code>imported_table_rpro</code> table.</p>
                <p><strong>Important:</strong> This is foundational data that other reconciliation processes depend on. The table will be truncated before import to ensure fresh data.</p>
                <p>The import processes records in batches of 1000 and typically takes several minutes to complete depending on the size of your inventory.</p>
            </div>
            <button id="importRproDataBtn" class="run-once-btn">Import RPRO Data</button>
            <div id="importRproDataOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsin Stats Into t_inv_sdasin</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_sdasin records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_sdasin_inventory</code> function which updates <code>t_inv_sdasin</code> records with correct quantity counts from the <code>v_stats_by_sdasin</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_sdasin</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileSdasinStatsBtn" class="run-once-btn">Reconcile SDAsin Stats</button>
            <div id="reconcileSdasinStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Import Veeqo Sellables</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Truncates the imported_table_veeqo_sellables_export table and runs the import_veeqo_sellables.js script.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>IMPORTANT STEP:</strong> Before clicking the button below, you must manually download the CSV export from Veeqo and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\veeqo_sellables_export.csv</code></p>
                <p>To download the export from Veeqo:</p>
                <ol>
                    <li>Log in to Veeqo</li>
                    <li>Go to Products</li>
                    <li>Click "Export" button</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, click the button below to import the data.</p>
            </div>
            <button id="importVeeqoSellablesBtn" class="run-worker-btn">Import Veeqo Sellables</button>
            <div id="importVeeqoSellablesOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Discs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Runs the reconcileDToVeeqo.js script to update discs with inventory discrepancies in Veeqo.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_d_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This script will update Veeqo quantities to 0 for sold discs that still show inventory in Veeqo.</p>
            </div>
            <button id="reconcileDToVeeqoBtn" class="run-once-btn">Reconcile Discs to Veeqo</button>
            <div id="reconcileDToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile RPRO to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Refreshes the reconcile_rpro_counts_to_veeqo table to compare RPRO and Veeqo quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process refreshes the <code>reconcile_rpro_counts_to_veeqo</code> table that compares quantities between RPRO (ivqtylaw) and Veeqo (total_qty).</p>
                <p>After running this, you can browse the <code>v_reconcile_rpro_counts_to_veeqo</code> view to see the discrepancies.</p>
            </div>
            <button id="reconcileRproToVeeqoBtn" class="run-once-btn">Refresh RPRO to Veeqo Reconciliation</button>
            <div id="reconcileRproToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Update Veeqo Quantities from RPRO</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates Veeqo product quantities to match RPRO quantities for records with discrepancies.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> This process will update Veeqo product quantities based on RPRO quantities for all records with discrepancies in the <code>v_reconcile_rpro_counts_to_veeqo</code> view.</p>
                <p>The following rules will be applied:</p>
                <ul>
                    <li>If RPRO quantity is negative, Veeqo quantity will be set to 0</li>
                    <li>If RPRO quantity has decimals, it will be rounded down (Veeqo doesn't support decimal quantities)</li>
                    <li>Otherwise, Veeqo quantity will be set equal to RPRO quantity</li>
                </ul>
                <p><strong>Note:</strong> It's recommended to run the "Refresh RPRO to Veeqo Reconciliation" process first to ensure you're working with the latest data.</p>
            </div>
            <button id="updateVeeqoFromRproBtn" class="run-once-btn">Update Veeqo from RPRO</button>
            <div id="updateVeeqoFromRproOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSLs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with inventory discrepancies in Veeqo and enqueues tasks to update their quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_osl_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This will enqueue tasks to update OSL quantities in Veeqo to match our local quantities.</p>
            </div>
            <button id="reconcileOSLToVeeqoBtn" class="run-once-btn">Reconcile OSLs to Veeqo</button>
            <div id="reconcileOSLToVeeqoOutput" class="output" style="display: none;"></div>
        </div>


        <div class="card">
            <div class="card-header">
                <h2>Import Amazon Active Listings Report</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Imports the most recent Amazon Active Listings Report into the it_amaz_active_listings_report table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>IMPORTANT STEP:</strong> Before clicking the button below, you must manually download the current Active Listings Report from Amazon and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\Amazon Active Listings Report\</code></p>
                <p>To download the report from Amazon:</p>
                <ol>
                    <li>Log in to Amazon Seller Central</li>
                    <li>Go to Inventory → Inventory Reports</li>
                    <li>Find "Active Listings Report" and click "Request Report"</li>
                    <li>Once generated, download the report (it will be named like "Active+Listings+Report+MM-DD-YYYY.txt")</li>
                    <li>Save it to the exact folder path specified above</li>
                </ol>
                <p><strong>Note:</strong> The import process will automatically find the most recent file that hasn't been imported yet and process it in chunks to handle large datasets.</p>
            </div>
            <button id="importAmazonActiveListingsBtn" class="run-once-btn">Import Amazon Active Listings Report</button>
            <div id="importAmazonActiveListingsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Download Shopify Matrixify Export</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Instructions for downloading the Matrixify export file from Shopify.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #9b59b6; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before proceeding with Shopify reconciliation, you need to download the latest Matrixify export file and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\shopify_matrixify_export_dg.csv</code></p>
                <p>To download the Matrixify export from Shopify:</p>
                <ol>
                    <li>Log in to your Shopify admin</li>
                    <li>Go to Apps > Matrixify</li>
                    <li>Select the "Export" tab</li>
                    <li>Choose the appropriate export template</li>
                    <li>Click "Export" and wait for the export to complete</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, you can proceed with the Shopify reconciliation tasks below.</p>
            </div>
            <button id="importShopifyMatrixifyBtn" class="run-once-btn">Import Shopify Matrixify Export to imported_table_shopify_products_dz</button>
            <div id="importShopifyMatrixifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Sold Discs on Shopify</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds sold discs still showing on Shopify and enqueues tasks to set their quantities to 0.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this, please browse through the <code>v_reconcile_d_to_shopify</code> view to review the discs with the issue "Sold disc still showing up on Shopify."</p>
                <p>This will enqueue tasks to set the Shopify inventory to 0 for sold discs. You can then process these tasks one at a time from the task queue.</p>
            </div>
            <button id="enqueueSoldDiscsShopifyBtn" class="run-once-btn">Enqueue Sold Discs Shopify Tasks</button>
            <div id="enqueueSoldDiscsShopifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsins to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds SDAsins with quantity discrepancies in Veeqo and enqueues tasks to update their quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this, please browse through the <code>v_reconcile_disc_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This will enqueue tasks to update SDAsin quantities in Veeqo to match our local available_quantity values. Only records with issue = "qtys don't match" will be processed.</p>
            </div>
            <button id="reconcileSdasinToVeeqoBtn" class="run-once-btn">Reconcile SDAsins to Veeqo</button>
            <div id="reconcileSdasinToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Generate OSL Fields for Null G_Code</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with null g_code and enqueues tasks to generate the fields.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Info:</strong> This will find OSLs where g_code is null and enqueue tasks to generate the fields.</p>
                <p>Each task will be processed by the worker to update the OSL record with generated fields.</p>
            </div>
            <div class="form-group">
                <label for="oslBatchSize">Batch Size</label>
                <input type="number" id="oslBatchSize" name="oslBatchSize" value="100" min="1" max="1000" placeholder="Number of OSLs to process">
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Maximum number of OSLs to process in one batch. Use a smaller number for quicker processing.
                    </span>
                </div>
            </div>
            <button id="enqueueGenerateOslFieldsBtn" class="run-once-btn">Enqueue Generate OSL Fields Tasks</button>
            <div id="enqueueGenerateOslFieldsOutput" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="worker" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Worker</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        The worker processes pending tasks from the queue.
                        It can be run once or continuously as a daemon.
                    </span>
                </div>
            </div>
            <div class="worker-controls">
                <div class="worker-mode-section">
                    <div class="form-group">
                        <label for="workerMode">Worker Mode</label>
                        <select id="workerMode">
                            <option value="once">Run Once</option>
                            <option value="daemon">Run as Daemon</option>
                        </select>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                "Run Once" processes pending tasks once and exits.
                                "Run as Daemon" continuously checks for new tasks every 15 seconds, processing up to 100 tasks per run.
                            </span>
                        </div>
                    </div>
                </div>
                <div class="worker-buttons">
                    <button id="runWorkerBtn" class="run-worker-btn">Run Worker</button>
                    <button id="stopWorkerBtn" class="stop-worker-btn" style="display: none;">Stop Worker</button>
                </div>
            </div>
            <div class="worker-status-section">
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerTabStatus">Stopped</span>
                </p>
                <p><strong>Last Run:</strong> <span id="workerTabLastRunTime">Unknown</span></p>
            </div>
            <div id="workerOutput" class="output" style="display: none;"></div>
        </div>

        <!-- Task Types Reference Section -->
        <div class="card">
            <div class="card-header">
                <h2>📋 Task Types Reference</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Complete reference of all task types handled by the worker daemon, including triggers, parent-child relationships, and timing delays.
                    </span>
                </div>
            </div>
            <div class="task-types-reference">
                <h3>🔄 Parent Tasks (Triggered by Database Changes)</h3>
                <div class="task-category">
                    <h4>📀 Disc Workflow Tasks</h4>
                    <div class="task-item">
                        <strong>new_t_discs_record</strong>
                        <p><strong>Triggered by:</strong> Database trigger when new record inserted into t_discs table</p>
                        <p><strong>Description:</strong> Parent task that spawns multiple child tasks for new disc processing</p>
                        <p><strong>Child Tasks:</strong></p>
                        <ul>
                            <li><code>generate_disc_title_pull_and_handle</code> - Immediate (0 minutes)</li>
                            <li><code>match_disc_to_osl</code> - 1 minute delay</li>
                            <li><code>match_disc_to_asins</code> - 3 minutes delay</li>
                            <li><code>set_disc_carry_cost</code> - 4 minutes delay</li>
                            <li><code>verify_disc_image</code> - 5 minutes delay (only if image_file_name exists)</li>
                            <li><code>check_if_disc_is_ready</code> - 6 minutes delay</li>
                        </ul>
                    </div>

                    <h4>📋 Order Sheet Line (OSL) Workflow Tasks</h4>
                    <div class="task-item">
                        <strong>osl_inserted</strong>
                        <p><strong>Triggered by:</strong> Database trigger when new record inserted into t_order_sheet_lines table</p>
                        <p><strong>Scheduled:</strong> 5 minutes after insertion</p>
                        <p><strong>Description:</strong> Parent task that spawns child tasks for new OSL processing</p>
                        <p><strong>Child Tasks:</strong></p>
                        <ul>
                            <li><code>osl_inserted_create_inv_osl</code> - Immediate (0 seconds)</li>
                            <li><code>generate_osl_fields</code> - 15 seconds delay</li>
                            <li><code>match_osl_to_discs</code> - 30 seconds delay</li>
                            <li><code>check_if_osl_is_ready</code> - 45 seconds delay</li>
                        </ul>
                    </div>

                    <div class="task-item">
                        <strong>osl_updated</strong>
                        <p><strong>Triggered by:</strong> Database trigger when t_order_sheet_lines record is updated</p>
                        <p><strong>Scheduled:</strong> 5 minutes after update</p>
                        <p><strong>Description:</strong> Parent task that spawns child tasks for OSL updates</p>
                        <p><strong>Child Tasks:</strong></p>
                        <ul>
                            <li><code>osl_updated_unlink_discs</code> - Immediate (0 seconds)</li>
                            <li><code>set_inv_osl_to_0</code> - 15 seconds delay</li>
                            <li><code>generate_osl_fields</code> - 30 seconds delay</li>
                            <li><code>match_osl_to_discs</code> - 30 seconds delay</li>
                            <li><code>check_if_osl_is_ready</code> - 45 seconds delay</li>
                        </ul>
                    </div>

                    <h4>🛒 Amazon/SDASIN Workflow Tasks</h4>
                    <div class="task-item">
                        <strong>sdasin_updated_find_discs_to_match</strong>
                        <p><strong>Triggered by:</strong> Database triggers on t_sdasins table for:</p>
                        <ul>
                            <li>INSERT operations</li>
                            <li>UPDATE of min_weight, max_weight, mps_id, mps_id2, color_id fields</li>
                            <li>UPDATE when looked_for_matching_discs_at is reset to NULL</li>
                        </ul>
                        <p><strong>Description:</strong> Finds and matches discs to Amazon listings (SDAsin records)</p>
                    </div>
                </div>

                <h3>🎯 Individual Task Types</h3>
                <div class="task-category">
                    <h4>📀 Disc Processing Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>generate_disc_title_pull_and_handle</strong>
                            <p>Updates t_discs.g_title, g_pull, and g_handle fields</p>
                        </div>
                        <div class="task-item">
                            <strong>match_disc_to_osl</strong>
                            <p>Matches discs to order sheet lines and updates inventory counts</p>
                        </div>
                        <div class="task-item">
                            <strong>match_disc_to_asins</strong>
                            <p>Matches discs to Amazon SDAsin records based on MPS, weight, and color</p>
                        </div>
                        <div class="task-item">
                            <strong>set_disc_carry_cost</strong>
                            <p>Calculates and updates t_discs.carrying_cost based on order cost and shipping</p>
                        </div>
                        <div class="task-item">
                            <strong>verify_disc_image</strong>
                            <p>Verifies disc images and updates t_discs.image_verified field</p>
                        </div>
                        <div class="task-item">
                            <strong>clear_disc_verification</strong>
                            <p>Clears previous verification data for discs</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_disc_is_ready</strong>
                            <p>Checks if disc meets all requirements and sets ready_new to TRUE</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_disc_ready_to_publish</strong>
                            <p>Checks if disc is in v_todo_discs and creates publish_disc task if ready</p>
                        </div>
                        <div class="task-item">
                            <strong>publish_disc</strong>
                            <p>Publishes disc to Shopify using publishProductDisc.js script</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>📋 Order Sheet Line (OSL) Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>osl_inserted_create_inv_osl</strong>
                            <p>Creates inventory record in t_inv_osl for new OSL</p>
                        </div>
                        <div class="task-item">
                            <strong>generate_osl_fields</strong>
                            <p>Updates OSL generated fields like g_code and other computed values</p>
                        </div>
                        <div class="task-item">
                            <strong>match_osl_to_discs</strong>
                            <p>Matches OSL to available discs using color and weight matching logic</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_osl_is_ready</strong>
                            <p>Verifies OSL has all required fields and related collections are published</p>
                        </div>
                        <div class="task-item">
                            <strong>set_inv_osl_to_0</strong>
                            <p>Sets OSL inventory to 0 (used when OSL is deleted or updated)</p>
                        </div>
                        <div class="task-item">
                            <strong>osl_updated_unlink_discs</strong>
                            <p>Unlinks discs from OSL when OSL is updated</p>
                        </div>
                        <div class="task-item">
                            <strong>publish_product_osl</strong>
                            <p>Publishes OSL to Shopify, scheduled based on release_date or 5 minutes future</p>
                        </div>
                        <div class="task-item">
                            <strong>check_discs_for_future_osl_publish</strong>
                            <p>Checks discs for OSL publish tasks scheduled >30 minutes in future</p>
                        </div>
                        <div class="task-item">
                            <strong>osl_uploaded_check_if_related_discs_are_ready</strong>
                            <p>Checks related discs when OSL is uploaded, schedules with 1-minute spacing</p>
                        </div>
                        <div class="task-item">
                            <strong>update_osl_after_publish</strong>
                            <p>Updates OSL record after successful Shopify publishing</p>
                        </div>
                        <div class="task-item">
                            <strong>toggle_osl_ready_button</strong>
                            <p>Toggles ready_button for OSLs to trigger upload process</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>🏭 MPS (Mold-Plastic-Stamp) Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>generate_mps_fields</strong>
                            <p>Updates t_mps.g_handle, g_flight_numbers, g_code, g_blurb_with_link, g_seo_metafield_description</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_mps_is_ready</strong>
                            <p>Triggered when t_mps plastic_id, mold_id, or stamp_id changes</p>
                        </div>
                        <div class="task-item">
                            <strong>plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload</strong>
                            <p>Finds MPS records with matching plastic_id and creates tasks for each</p>
                        </div>
                        <div class="task-item">
                            <strong>mps_price_verified_try_upload_osls</strong>
                            <p>Creates tasks for uploaded OSLs and schedules tasks for OSLs needing upload</p>
                        </div>
                        <div class="task-item">
                            <strong>mps_price_verified_osl_uploaded_look_for_discs</strong>
                            <p>Sets ready_new to FALSE for discs with matching order_sheet_line_id</p>
                        </div>
                        <div class="task-item">
                            <strong>mps_published_check_if_related_osls_are_ready</strong>
                            <p>Checks related OSLs when MPS is published, schedules with 1-minute spacing</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>🏷️ Brand, Mold, Plastic, and Collection Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>check_if_brand_is_ready</strong>
                            <p>Triggered when t_brands fields change, checks if brand ready for Shopify</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_mold_is_ready</strong>
                            <p>Triggered when t_molds or t_brands shopify_collection_created_at changes</p>
                        </div>
                        <div class="task-item">
                            <strong>check_if_plastic_is_ready</strong>
                            <p>Triggered when t_plastics fields change, checks readiness for publishing</p>
                        </div>
                        <div class="task-item">
                            <strong>publish_collection_brand</strong>
                            <p>Publishes brand collection to Shopify</p>
                        </div>
                        <div class="task-item">
                            <strong>publish_collection_mold</strong>
                            <p>Publishes mold collection to Shopify</p>
                        </div>
                        <div class="task-item">
                            <strong>publish_collection_plastic</strong>
                            <p>Publishes plastic collection to Shopify</p>
                        </div>
                        <div class="task-item">
                            <strong>brand_collection_published</strong>
                            <p>Post-processing after brand collection is published</p>
                        </div>
                        <div class="task-item">
                            <strong>mold_collection_published</strong>
                            <p>Post-processing after mold collection is published</p>
                        </div>
                        <div class="task-item">
                            <strong>plastic_collection_published</strong>
                            <p>Post-processing after plastic collection is published</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>📷 Image Processing Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>verify_t_images_image</strong>
                            <p>Verifies images in the legacy t_images table</p>
                        </div>
                        <div class="task-item">
                            <strong>insert_new_t_images_record</strong>
                            <p>Creates new record in t_images table</p>
                        </div>
                        <div class="task-item">
                            <strong>delete_t_images_record</strong>
                            <p>Deletes record from t_images table</p>
                        </div>
                        <div class="task-item">
                            <strong>clear_incorrect_mps_image</strong>
                            <p>Deletes t_images record and corresponding S3 file in mps folder</p>
                        </div>
                        <div class="task-item">
                            <strong>rename_and_upload_images</strong>
                            <p>Renames and uploads images from Google Drive folders</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>📦 Inventory and Veeqo Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>disc_sold_update_veeqo_qty_by_d_sku</strong>
                            <p>Sets Veeqo quantity to 0 when disc is sold</p>
                        </div>
                        <div class="task-item">
                            <strong>disc_unsold_update_veeqo_qty_by_d_sku</strong>
                            <p>Sets Veeqo quantity to 1 when disc is unsold</p>
                        </div>
                        <div class="task-item">
                            <strong>reconcile_clear_count_from_shopify_for_sold_disc</strong>
                            <p>Sets Shopify inventory to 0 for sold discs still showing on Shopify</p>
                        </div>
                        <div class="task-item">
                            <strong>update_shopify_inventory_to_zero</strong>
                            <p>Updates Shopify inventory to zero for specific products</p>
                        </div>
                        <div class="task-item">
                            <strong>update_veeqo_inventory</strong>
                            <p>Updates Veeqo inventory quantities</p>
                        </div>
                        <div class="task-item">
                            <strong>update_veeqo_sdasin_qty</strong>
                            <p>Updates Veeqo quantities for SDAsin records to match local available_quantity</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>🛒 Amazon/FBA Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>delete_amazon_listing</strong>
                            <p>Deletes Amazon marketplace listings via SP-API</p>
                        </div>
                        <div class="task-item">
                            <strong>update_fba_inventory_recommendations</strong>
                            <p>Updates FBA inventory recommendations based on sales rank</p>
                        </div>
                        <div class="task-item">
                            <strong>reset_fbm_inactive_records</strong>
                            <p>Resets FBM records for inactive MPS items</p>
                        </div>
                        <div class="task-item">
                            <strong>reset_fba_inactive_records</strong>
                            <p>Resets FBA records for inactive MPS items</p>
                        </div>
                    </div>
                </div>

                <div class="task-category">
                    <h4>⚙️ System and Status Tasks</h4>
                    <div class="task-grid">
                        <div class="task-item">
                            <strong>worker_daemon_online</strong>
                            <p>Heartbeat task indicating worker daemon is running (hourly status updates)</p>
                        </div>
                        <div class="task-item">
                            <strong>test_task</strong>
                            <p>Test task for debugging and validation purposes</p>
                        </div>
                    </div>
                </div>

                <h3>⏰ Task Scheduling and Timing</h3>
                <div class="timing-info">
                    <h4>🔄 Standard Scheduling Patterns</h4>
                    <ul>
                        <li><strong>Immediate:</strong> NOW() - Tasks that need to run right away</li>
                        <li><strong>5 minutes future:</strong> NOW() + INTERVAL '5 minutes' - Default for most parent tasks</li>
                        <li><strong>Release date scheduling:</strong> Uses release_date when available for publish tasks</li>
                        <li><strong>Retry scheduling:</strong> Failed Shopify tasks retry 5 minutes later (once)</li>
                    </ul>

                    <h4>⚡ Child Task Delays (from parent task execution)</h4>
                    <ul>
                        <li><strong>0 seconds:</strong> Immediate child tasks</li>
                        <li><strong>15 seconds:</strong> generate_osl_fields, set_inv_osl_to_0</li>
                        <li><strong>30 seconds:</strong> match_osl_to_discs</li>
                        <li><strong>45 seconds:</strong> check_if_osl_is_ready</li>
                        <li><strong>1 minute:</strong> match_disc_to_osl</li>
                        <li><strong>3 minutes:</strong> match_disc_to_asins</li>
                        <li><strong>4 minutes:</strong> set_disc_carry_cost</li>
                        <li><strong>5 minutes:</strong> verify_disc_image (if image exists)</li>
                        <li><strong>6 minutes:</strong> check_if_disc_is_ready</li>
                        <li><strong>1-minute spacing:</strong> Multiple related tasks scheduled with incremental delays</li>
                    </ul>

                    <h4>🔄 Worker Daemon Operation</h4>
                    <ul>
                        <li><strong>Run Interval:</strong> Every 15 seconds</li>
                        <li><strong>Batch Size:</strong> Up to 100 tasks per run</li>
                        <li><strong>Task Locking:</strong> Uses locked_at and locked_by fields to prevent duplicate processing</li>
                        <li><strong>Status Updates:</strong> Hourly worker_daemon_online heartbeat tasks</li>
                    </ul>
                </div>

                <h3>🎯 Task Triggers Summary</h3>
                <div class="triggers-summary">
                    <h4>📊 Database Triggers</h4>
                    <ul>
                        <li><strong>t_discs INSERT:</strong> → new_t_discs_record</li>
                        <li><strong>t_order_sheet_lines INSERT:</strong> → osl_inserted (5 min delay)</li>
                        <li><strong>t_order_sheet_lines UPDATE:</strong> → osl_updated (5 min delay)</li>
                        <li><strong>t_order_sheet_lines DELETE:</strong> → set_inv_osl_to_0 (5 min delay)</li>
                        <li><strong>t_sdasins INSERT/UPDATE:</strong> → sdasin_updated_find_discs_to_match</li>
                        <li><strong>t_mps plastic_id/mold_id/stamp_id changes:</strong> → check_if_mps_is_ready</li>
                        <li><strong>t_order_sheet_lines shopify_uploaded_at changes:</strong> → osl_uploaded_check_if_related_discs_are_ready</li>
                        <li><strong>t_brands/t_molds/t_plastics changes:</strong> → Various readiness check tasks</li>
                    </ul>

                    <h4>🔧 Manual/API Triggers</h4>
                    <ul>
                        <li><strong>Admin Interface:</strong> Various one-time tasks and bulk operations</li>
                        <li><strong>Webhooks:</strong> External system integrations</li>
                        <li><strong>Scheduled Jobs:</strong> Automated workflows (Discraft, Informed, etc.)</li>
                        <li><strong>Error Recovery:</strong> Retry mechanisms for failed tasks</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div id="flow" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Workflows</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This section shows all the task types handled by the worker, including the disc publishing workflow and additional task types for field generation, matching, and inventory management.
                    </span>
                </div>
            </div>
            <div class="workflow-diagram">
                <h3>Workflow Steps</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Image File Name Update</strong>
                        <p>When a disc's image_file_name is updated in t_discs, two tasks are created:</p>
                        <ul>
                            <li><code>verify_disc_image</code> - Verifies the new image</li>
                            <li><code>clear_disc_verification</code> - Clears previous verification data</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Image Verification</strong>
                        <p>The worker processes the <code>verify_disc_image</code> task:</p>
                        <ul>
                            <li>Checks if the image is accessible</li>
                            <li>Updates t_discs.image_verified and related fields</li>
                            <li>If successful, triggers the next step</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Readiness Check</strong>
                        <p>When image_verified is set to TRUE, a <code>check_if_disc_is_ready</code> task is created:</p>
                        <ul>
                            <li>Checks if all required fields are not null</li>
                            <li>Checks if ready_button is TRUE</li>
                            <li>Checks if image_verified is TRUE</li>
                            <li>Checks if shopify_uploaded_at is NULL</li>
                            <li>Checks if sold_date is NULL (disc not already sold)</li>
                            <li>If all conditions are met, sets t_discs.ready_new to TRUE</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing Check</strong>
                        <p>When ready_new is set to TRUE, a <code>check_if_disc_ready_to_publish</code> task is created:</p>
                        <ul>
                            <li>Checks if the disc is in v_todo_discs</li>
                            <li>If not, creates a <code>publish_disc</code> task</li>
                            <li>If it is, updates t_discs with a note</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing to Shopify</strong>
                        <p>The worker processes the <code>publish_disc</code> task:</p>
                        <ul>
                            <li>Runs the publishProductDisc.js script</li>
                            <li>Updates t_discs.shopify_uploaded_at when successful</li>
                            <li>This completes the workflow</li>
                        </ul>
                    </li>
                </ol>

                <h3>Additional Task Types</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Field Generation Tasks</strong>
                        <p>Tasks that generate and update fields in the database:</p>
                        <ul>
                            <li><code>generate_disc_title_pull_and_handle</code> - Updates t_discs.g_title, t_discs.g_pull, and t_discs.g_handle</li>
                            <li><code>generate_mps_fields</code> - Updates t_mps.g_handle, t_mps.g_flight_numbers, t_mps.g_code, t_mps.g_blurb_with_link, and t_mps.g_seo_metafield_description</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Matching and Inventory Tasks</strong>
                        <p>Tasks that handle disc matching and inventory management:</p>
                        <ul>
                            <li><code>set_disc_carry_cost</code> - Calculates and updates t_discs.carrying_cost based on order cost and shipping multiplier</li>
                            <li><code>match_disc_to_asins</code> - Matches discs to SDASINs based on MPS, weight, and color</li>
                            <li><code>match_disc_to_osl</code> - Matches discs to order sheet lines and updates inventory counts</li>
                            <li><code>plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload</code> - Finds MPS records with matching plastic_id and creates tasks for each one</li>
                            <li><code>mps_price_verified_try_upload_osls</code> - Creates tasks for already uploaded OSLs and schedules tasks for OSLs that need uploading</li>
                            <li><code>mps_price_verified_osl_uploaded_look_for_discs</code> - Sets ready_new to FALSE for discs with matching order_sheet_line_id that are ready to publish</li>
                            <li><code>toggle_osl_ready_button</code> - Toggles ready_button for OSLs to trigger the upload process</li>
                            <li><code>reconcile_clear_count_from_shopify_for_sold_disc</code> - Sets Shopify inventory to zero for sold discs still showing on Shopify</li>
                            <li><code>update_osl_after_publish</code> - Updates OSL record after successful publishing to Shopify</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Legacy Image Tasks</strong>
                        <p>Tasks that handle image records in the t_images table:</p>
                        <ul>
                            <li><code>verify_t_images_image</code> - Verifies images in the t_images table</li>
                            <li><code>insert_new_t_images_record</code> - Creates a new record in the t_images table</li>
                            <li><code>delete_t_images_record</code> - Deletes a record from the t_images table</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <!-- MPS Collections Cleanup Section -->
        <div class="card" style="border: 2px solid #e74c3c; background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);">
            <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white;">
                <h2>🗑️ MPS Collections Cleanup</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This tool identifies and removes old, empty MPS collections from Shopify that are taking up space in your 5,000 collection limit.
                        MPS collections have 3 tag conditions: disc_mold_xxx, disc_plastic_xxx, and disc_stamp_xxx.
                        Only empty collections (no products) will be deleted, and corresponding database records will be updated.
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #fdf2f2; border-left-color: #e74c3c; padding: 15px; margin-bottom: 20px;">
                    <h4>⚠️ What This Tool Does:</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Identifies:</strong> The 10 oldest empty MPS collections on Shopify</li>
                        <li><strong>Verifies:</strong> Collections have no products (completely empty)</li>
                        <li><strong>Deletes:</strong> Empty collections from Shopify</li>
                        <li><strong>Updates:</strong> Sets t_mps.shopify_collection_uploaded_at to NULL and active to FALSE for matching database records</li>
                        <li><strong>Safe:</strong> Only removes collections that are confirmed empty</li>
                    </ul>
                    <p><strong>Current Status:</strong> You have 5,000/5,000 collections (at limit). This tool will free up space for new collections.</p>
                </div>

                <div class="button-row" style="margin-bottom: 20px;">
                    <button class="workflow-btn" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);" onclick="previewMpsCollectionsCleanup()">
                        🔍 Preview Collections to Delete
                    </button>
                    <button id="deleteMpsCollectionsBtn" class="workflow-btn" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); display: none;" onclick="confirmMpsCollectionsCleanup()">
                        🗑️ Delete Selected Collections
                    </button>
                </div>

                <div id="mpsCollectionsPreview" class="output" style="display: none;"></div>
                <div id="mpsCollectionsCleanupOutput" class="output" style="display: none;"></div>
            </div>
        </div>


    </div>

    <div id="discraft" class="tab-content">
        <!-- Discraft Workflow Section -->
        <div class="card" style="border: 2px solid #9b59b6; background: linear-gradient(135deg, #f8f5ff 0%, #ffffff 100%);">
            <div class="card-header" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white;">
                <h2>🥏 Discraft Vendor Workflow</h2>
                <p>Automated vendor catalog import and order management</p>
            </div>
            <div class="card-body">
                <!-- Step 1: File Download Instructions -->
                <div class="step-group">
                    <h3>📁 Step 1: Download Vendor File</h3>
                    <div class="info-box" style="background: #e8f4fd; border-left-color: #3498db; padding: 15px;">
                        <h4>📋 Automated Download:</h4>
                        <p>Click the button below to automatically download the latest Discraft order sheet and save it to the correct location.</p>
                        <div class="button-row">
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);" onclick="downloadDiscraftVendorFile()">
                                📥 Download Vendor File
                            </button>
                        </div>
                        <div id="discraftDownloadOutput" class="output" style="display: none;"></div>

                        <h4 style="margin-top: 20px;">📋 Manual Instructions (if needed):</h4>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Download</strong> the latest Discraft order sheet from: <a href="https://www.discgolf.discraft.com/forms/stock.xlsx" target="_blank">https://www.discgolf.discraft.com/forms/stock.xlsx</a></li>
                            <li><strong>Save as:</strong> <code>discraftstock.xlsx</code></li>
                            <li><strong>Location:</strong> <code>C:\Users\<USER>\supabase_project\data\external data\discraftstock.xlsx</code></li>
                            <li><strong>Replace</strong> any existing file with the same name</li>
                        </ol>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px;">
                            <strong>⚠️ Important:</strong> The file must be named exactly <code>discraftstock.xlsx</code> and placed in the correct folder for the import to work.
                        </div>
                    </div>
                </div>

                <!-- Step 2: Import & Calculate Matches -->
                <div class="step-group">
                    <h3>🔄 Step 2: Import & Calculate Matches</h3>
                    <div class="info-box" style="background: #e8f5e8; border-left-color: #27ae60; padding: 15px;">
                        <h4>📊 Import & Matching Process:</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Import Catalog:</strong> Parse vendor file and import products</li>
                            <li><strong>Calculate MPS IDs:</strong> Match products to your inventory</li>
                            <li><strong>Analyze Results:</strong> Review matching success and failures</li>
                        </ul>
                        <div class="button-row">
                            <button class="workflow-btn" onclick="runDiscraftImport()">
                                🚀 Run Full Import & Matching
                            </button>
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);" onclick="analyzeMatching()">
                                📊 Analyze Matching Results
                            </button>
                        </div>
                        <div id="discraftImportOutput" class="output" style="display: none;"></div>
                    </div>
                </div>

                <!-- Step 3: Review Parsing Issues -->
                <div class="step-group">
                    <h3>🔧 Step 3: Review Parsing Issues</h3>
                    <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px;">
                        <h4>🔍 Identify Parsing Problems:</h4>
                        <p>Review vendor products that don't match your MPS records to identify parsing fixes needed.</p>
                        <div class="button-row">
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);" onclick="reviewParsingIssues()">
                                🔧 Review Parsing Issues
                            </button>
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);" onclick="reviewUnmatchedOsls()">
                                📋 Review Unmatched OSLs
                            </button>
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);" onclick="reviewUnmatchedDiscraftProducts()">
                                🎯 Review Unmatched Discraft Products
                            </button>
                        </div>
                        <div id="oslMatchingOutput" class="output" style="display: none;"></div>
                        <div id="unmatchedOslsTable" style="display: none; margin-top: 20px;">
                            <div style="margin-bottom: 15px;">
                                <label style="display: flex; align-items: center; gap: 8px; font-weight: bold;">
                                    <input type="checkbox" id="showInactiveMps" onchange="toggleInactiveMps()">
                                    Show Inactive MPS Records
                                </label>
                            </div>
                            <div id="tableRecordCount" style="margin-bottom: 10px; font-weight: bold; color: #666;"></div>
                            <div style="overflow: auto; max-height: 800px; border: 1px solid #ddd;">
                                <table id="unmatchedOslsTableContent" class="data-table" style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f5f5f5; position: sticky; top: 0; z-index: 10;">
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(0)" title="Click to sort">
                                                OSL ID <span id="sort-0">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(1)" title="Click to sort">
                                                MPS ID <span id="sort-1">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(2)" title="Click to sort">
                                                Plastic <span id="sort-2">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(3)" title="Click to sort">
                                                Mold <span id="sort-3">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(4)" title="Click to sort">
                                                Stamp <span id="sort-4">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(5)" title="Click to sort">
                                                Weight <span id="sort-5">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(6)" title="Click to sort">
                                                Color <span id="sort-6">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: left; cursor: pointer; user-select: none;" onclick="sortTable(7)" title="Click to sort">
                                                MPS Active <span id="sort-7">⇅</span>
                                            </th>
                                            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="unmatchedOslsTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Export Order Data -->
                <div class="step-group">
                    <h3>📤 Step 4: Export Order Data</h3>
                    <div class="info-box" style="background: #e8f5e8; border-left-color: #27ae60; padding: 15px;">
                        <h4>📊 Export Process:</h4>
                        <p>Export order quantities from <code>v_stats_by_osl_discraft.order</code> back into a copy of the Discraft spreadsheet.</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Source Data:</strong> Uses calculated order quantities from the view</li>
                            <li><strong>Output File:</strong> Creates copy of original spreadsheet with today's date</li>
                            <li><strong>Format:</strong> <code>discraftstock_YYYY-MM-DD.xlsx</code></li>
                            <li><strong>Location:</strong> Same folder as original file</li>
                        </ul>
                        <div class="button-row">
                            <button class="workflow-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);" onclick="exportDiscraftOrders()">
                                📤 Export Order Data to Excel
                            </button>
                        </div>
                        <div id="discraftExportOutput" class="output" style="display: none;"></div>
                    </div>
                </div>

                <!-- Import Statistics -->
                <div class="step-group">
                    <h3>📈 Import Statistics</h3>
                    <div id="discraftStats" class="info-box" style="background: #f8f9fa; border-left-color: #6c757d; padding: 15px;">
                        <p>Import statistics will appear here after running the import...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daily Automation Scheduler -->
        <div class="card" style="border: 2px solid #e67e22; background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);">
            <div class="card-header" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%); color: white;">
                <h2>⏰ Daily Automation Scheduler</h2>
                <p>Automated daily Discraft workflow at 11:00 AM CST</p>
            </div>
            <div class="card-body">
                <!-- Scheduler Status -->
                <div class="step-group">
                    <h3>📊 Scheduler Status</h3>
                    <div id="discraftSchedulerStatus" class="info-box" style="background: #f8f9fa; border-left-color: #6c757d; padding: 15px;">
                        <p>Loading scheduler status...</p>
                    </div>
                </div>

                <!-- Configuration -->
                <div class="step-group">
                    <h3>⚙️ Configuration</h3>
                    <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px;">
                        <div class="form-group">
                            <label for="exportThreshold">Export Threshold (discs):</label>
                            <input type="number" id="exportThreshold" min="1" max="1000" value="100" style="width: 100px;">
                            <span style="margin-left: 10px; color: #666;">Export Excel file when order quantity exceeds this threshold</span>
                        </div>
                        <div class="button-row">
                            <button class="workflow-btn" onclick="updateExportThreshold()">💾 Update Threshold</button>
                        </div>
                        <div id="thresholdUpdateOutput" class="output" style="display: none;"></div>
                    </div>
                </div>

                <!-- Scheduler Controls -->
                <div class="step-group">
                    <h3>🎮 Scheduler Controls</h3>
                    <div class="button-row">
                        <button class="workflow-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);" onclick="startDiscraftScheduler()">
                            ▶️ Start Scheduler
                        </button>
                        <button class="workflow-btn" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);" onclick="stopDiscraftScheduler()">
                            ⏹️ Stop Scheduler
                        </button>
                        <button class="workflow-btn" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);" onclick="testDiscraftAutomation()">
                            🧪 Test Automation Now
                        </button>
                    </div>
                    <div id="discraftSchedulerOutput" class="output" style="display: none;"></div>
                </div>

                <!-- Scheduler Logs -->
                <div class="step-group">
                    <h3>📋 Scheduler Logs</h3>
                    <div class="button-row">
                        <button class="workflow-btn" onclick="viewDiscraftLogs()">📄 View Logs</button>
                        <button class="workflow-btn" onclick="clearDiscraftLogs()">🗑️ Clear Logs</button>
                    </div>
                    <div id="discraftLogsContainer" style="display: none;">
                        <div class="console-controls">
                            <label class="auto-scroll-label">
                                <input type="checkbox" id="autoScrollLogs" checked>
                                Auto-scroll
                            </label>
                            <button class="clear-console-btn" onclick="clearDiscraftLogs()">Clear</button>
                        </div>
                        <div id="discraftLogs" class="console-log" style="max-height: 400px;"></div>
                    </div>
                </div>

                <!-- Automation Info -->
                <div class="step-group">
                    <h3>ℹ️ Automation Details</h3>
                    <div class="info-box" style="background: #e8f4fd; border-left-color: #3498db; padding: 15px;">
                        <h4>📅 Daily Schedule:</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Time:</strong> 11:00 AM CST (Central Standard Time)</li>
                            <li><strong>Frequency:</strong> Every day</li>
                            <li><strong>Timezone:</strong> America/Chicago (handles DST automatically)</li>
                        </ul>

                        <h4>🔄 Automation Process:</h4>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Download:</strong> Latest Discraft vendor file</li>
                            <li><strong>Import:</strong> Parse and import all products</li>
                            <li><strong>Match:</strong> Calculate MPS IDs for inventory alignment</li>
                            <li><strong>Calculate:</strong> Generate order quantities</li>
                            <li><strong>Export:</strong> Create Excel file if order quantity ≥ threshold</li>
                            <li><strong>Email:</strong> Send detailed report with attachment (if exported)</li>
                        </ol>

                        <h4>📧 Email Report Includes:</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Total discs</strong> to order</li>
                            <li><strong>Top 10 molds</strong> by order quantity</li>
                            <li><strong>Process status</strong> for each step</li>
                            <li><strong>Excel attachment</strong> (when order quantity ≥ threshold)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="informed" class="tab-content">
        <!-- Main Workflow Section -->
        <div class="card" style="border: 2px solid #e67e22; background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);">
            <div class="card-header gradient-orange">
                <h2>🚀 Complete Informed Workflow</h2>
                <p>End-to-end automation for Informed Repricer management</p>
            </div>
            <div class="card-body">
                <div class="workflow-section">
                    <div class="info-box" style="background: #fff3cd; border-left-color: #f39c12; padding: 15px; margin-bottom: 20px;">
                        <h3>🔄 Full Automation Process</h3>
                        <p><strong>Complete 3-phase workflow:</strong> Download reports → Import data → Upload pricing</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Phase 1:</strong> Download latest reports from Informed API</li>
                            <li><strong>Phase 2:</strong> Import reports into Supabase tables</li>
                            <li><strong>Phase 3:</strong> Generate and upload pricing data with REAL calculations</li>
                        </ul>
                        <p><strong>📅 Scheduled:</strong> Runs automatically at 6:30 AM, 12:30 PM, and 6:30 PM CST</p>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button id="runCompleteWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                            ⚡ Run Complete Workflow
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs the complete end-to-end workflow: Downloads reports from Informed, imports them to Supabase, then uploads pricing data back to Informed. This is the full 3-step process that should be scheduled 3 times per day.
                            </span>
                        </div>
                    </div>
                    <div id="runCompleteWorkflowOutput" class="enhanced-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Data Management Section -->
        <div class="card">
            <div class="card-header gradient-blue">
                <h2>📊 Data Management</h2>
                <p>Download and import Informed reports</p>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px; margin-bottom: 20px;">
                    <h4>📋 Report Types</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>All Fields</strong> → <code>it_infor_all_fields</code></li>
                        <li><strong>Competition Landscape</strong> → <code>it_infor_competition_landscape</code></li>
                        <li><strong>No Buy Box</strong> → <code>it_infor_no_buy_box</code></li>
                    </ul>
                </div>

                <div class="step-group">
                    <h3>📊 Report Status</h3>
                    <div id="informedReportStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading report status...</p>
                    </div>
                </div>

                <div class="step-group">
                    <h3>📥 Download & Import</h3>
                    <div class="button-row">
                        <div class="button-group">
                            <button id="downloadInformedReportsBtn" class="run-once-btn step-btn">📥 Download Reports</button>
                            <small>Get latest data from Informed</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="maxRetries">Max Retries:</label>
                                <input type="number" id="maxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="retryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="retryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="button-group">
                            <button id="importInformedReportsBtn" class="run-once-btn step-btn">📋 Import Reports</button>
                            <small>Process into Supabase tables</small>
                        </div>

                        <div class="button-group">
                            <button id="runFullInformedProcessBtn" class="run-worker-btn workflow-btn">🔄 Download + Import</button>
                            <small>Combined workflow</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="fullProcessMaxRetries">Max Retries:</label>
                                <input type="number" id="fullProcessMaxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="fullProcessRetryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="fullProcessRetryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="downloadInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="importInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="runFullInformedProcessOutput" class="step-output" style="display: none;"></div>
                </div>

                <div class="step-group">
                    <h3>⏰ Scheduler Status</h3>
                    <div id="informedSchedulerStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading scheduler status...</p>
                    </div>

                    <div class="button-row">
                        <button id="enableInformedSchedulerBtn" class="run-worker-btn">✅ Enable Scheduler</button>
                        <button id="disableInformedSchedulerBtn" class="stop-worker-btn">❌ Disable Scheduler</button>
                    </div>
                    <div id="informedSchedulerOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Pricing Upload Section -->
        <div class="card">
            <div class="card-header gradient-green">
                <h2>💰 Pricing Upload</h2>
                <p>Generate and upload pricing data to Informed</p>
            </div>
            <div class="card-body">
                <div class="info-box success-highlight" style="margin-bottom: 20px;">
                    <h4>📈 Pricing Process Overview</h4>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Truncate:</strong> Clear old pricing data from tu_informed table</li>
                        <li><strong>Fill:</strong> Populate with fresh data using REAL pricing calculations</li>
                        <li><strong>Export:</strong> Generate CSV file formatted for Informed API</li>
                        <li><strong>Upload:</strong> Send pricing data to Informed Repricer</li>
                    </ol>
                    <p style="margin: 10px 0 0 0; font-style: italic; color: #27ae60;">
                        ✅ Now using REAL MAP prices from your MPS and plastics data (no more hardcoded values!)
                    </p>
                </div>

                <div class="step-group">
                    <h3>🔧 Individual Steps</h3>
                    <div class="button-grid">
                        <div class="button-group">
                            <button id="truncateTuInformedBtn" class="run-once-btn step-btn">🗑️ 1. Truncate</button>
                            <small>Clear old data</small>
                        </div>

                        <div class="button-group">
                            <button id="fillTuInformedBtn" class="run-once-btn step-btn">📊 2. Fill Data</button>
                            <small>Real pricing calculations</small>
                        </div>

                        <div class="button-group">
                            <button id="exportCsvBtn" class="run-once-btn step-btn">📄 3. Export CSV</button>
                            <small>Generate upload file</small>
                        </div>

                        <div class="button-group">
                            <button id="uploadCsvBtn" class="run-once-btn step-btn">🚀 4. Upload</button>
                            <small>Send to Informed</small>
                        </div>
                    </div>
                </div>

                <div class="step-group" style="margin-top: 30px;">
                    <h3>⚡ Complete Upload Workflow</h3>
                    <div style="text-align: center;">
                        <button id="runUploadWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px;">
                            🔄 Run All Upload Steps
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs all 4 upload steps in sequence: Truncate → Fill → Export → Upload. Uses optimized real pricing calculations.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="truncateTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="fillTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="exportCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="uploadCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="runUploadWorkflowOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="innova" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>📦 Innova Order Import</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Import Innova order data from Excel files into the it_innova_order_sheet_lines table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Download the Innova order form Excel file</li>
                    <li>Save it as <code>innovaorderform.xlsx</code> in the <code>data/external data</code> folder</li>
                    <li>Click the "Import Innova Order Data" button below</li>
                    <li>All existing data will be replaced with the new import</li>
                </ol>
                <p><strong>Expected File Location:</strong> <code>C:\Users\<USER>\supabase_project\data\external data\innovaorderform.xlsx</code></p>
                <p><strong>Data Source:</strong> The import reads from the "Order_Table" sheet in the Excel file</p>
                <p><strong>⚠️ Important:</strong> This import will truncate and replace all existing data in the table</p>
            </div>

            <div class="step-group">
                <h3>📋 Import Process</h3>
                <div class="button-row">
                    <button id="importInnovaDataBtn" class="step-btn">1. Import Data (Truncate & Replace)</button>
                    <button id="viewInnovaDataBtn" class="step-btn">2. View Data</button>
                </div>
                <div class="info-box">
                    <p><strong>Step 1:</strong> Truncates existing data and imports fresh data from the Excel file</p>
                    <p><strong>Step 2:</strong> Shows a sample of the imported data for verification</p>
                    <p><strong>Note:</strong> The import will replace all existing data in the table</p>
                </div>
            </div>

            <div class="step-group">
                <h3>⚙️ Configuration</h3>
                <div class="button-row">
                    <button id="showPlasticMappingBtn" class="step-btn">🔧 Manage Plastic Mappings</button>
                </div>
                <div class="info-box">
                    <p><strong>Plastic Mappings:</strong> Configure how your plastic names map to Innova's plastic names</p>
                    <p><strong>Example:</strong> Your "KC Pro" = Innova's "Pro KC"</p>
                </div>
            </div>

            <div class="step-group">
                <h3>🔄 Phase 1: Validate Existing Matches</h3>
                <div class="button-row">
                    <button id="validateExistingMatchesBtn" class="step-btn">🔍 Basic Validation (String Similarity)</button>
                    <button id="enhancedMatchingBtn" class="step-btn">🚀 Enhanced Matching (Relational)</button>
                    <button id="validateWithClearBtn" class="step-btn">⚠️ Validate & Auto-Clear Broken Links</button>
                    <button id="viewValidationReportBtn" class="step-btn">📊 View Validation Report</button>
                </div>
                <div class="info-box">
                    <p><strong>🔍 Basic Validation:</strong> String similarity between g_code and description</p>
                    <p><strong>🚀 Enhanced Matching:</strong> Uses relational data (mold, plastic, weight) for precise matching</p>
                    <p><strong>⚠️ Auto-Clear:</strong> Clears broken links after validation</p>
                    <p><strong>Safety:</strong> Only processes vendor_id=2 (Innova) records</p>
                </div>
            </div>

            <div id="innovaOutput" class="output" style="display: none;"></div>

            <div id="validationTableContainer" style="display: none; margin-top: 20px; width: 100%; overflow-x: auto;">
                <h3>📊 Validation Results</h3>
                <div style="margin-bottom: 10px;">
                    <label>Filter by Status: </label>
                    <select id="statusFilter">
                        <option value="">All</option>
                        <option value="🟢 PERFECT_MATCH">🟢 Perfect Match</option>
                        <option value="🟡 GOOD_MATCH">🟡 Good Match</option>
                        <option value="🟠 PARTIAL_MATCH">🟠 Partial Match</option>
                        <option value="🔴 POOR_MATCH">🔴 Poor Match</option>
                        <option value="🔴 BROKEN_LINK">🔴 Broken Link</option>
                        <!-- Legacy options for basic validation -->
                        <option value="🟢 HIGH_CONFIDENCE">🟢 High Confidence</option>
                        <option value="🟡 MEDIUM_CONFIDENCE">🟡 Medium Confidence</option>
                        <option value="🟠 LOW_CONFIDENCE">🟠 Low Confidence</option>
                    </select>

                    <label style="margin-left: 20px;">Filter by Score: </label>
                    <select id="confidenceFilter">
                        <option value="">All Scores</option>
                        <option value="100">100% Only</option>
                        <option value="90+">90%+ Only</option>
                        <option value="80+">80%+ Only</option>
                        <option value="70+">70%+ Only</option>
                    </select>

                    <label style="margin-left: 20px;">Filter by OSL Stamp: </label>
                    <select id="stampFilter">
                        <option value="">All Stamps</option>
                        <option value="Stock">Stock</option>
                        <option value="Burst Logo Stock">Burst Logo Stock</option>
                        <option value="Circle Fade Stock">Circle Fade Stock</option>
                        <option value="Stock Character">Stock Character</option>
                        <option value="Character">Character</option>
                        <option value="15 Year Commemorative">15 Year Commemorative</option>
                        <option value="Proto">Proto</option>
                        <option value="Special Edition">Special Edition</option>
                    </select>

                    <label style="margin-left: 20px;">Min Score: </label>
                    <input type="number" id="similarityFilter" min="0" max="100" placeholder="0-100">

                    <button id="applyFilters" style="margin-left: 10px;">Apply Filters</button>
                    <button id="clearFilters" style="margin-left: 5px;">Clear</button>
                </div>

                <div style="max-height: 1800px; overflow-y: auto; border: 1px solid #ddd;">
                    <table id="validationTable" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                        <thead style="background-color: #f5f5f5; position: sticky; top: 0;">
                            <tr id="tableHeaders">
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="g_code">G-Code ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight Match ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="string_similarity">Similarity % ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="validationTableBody">
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 5px;">
                    <div style="margin-bottom: 10px;">
                        <strong>Quick Select:</strong>
                        <button id="selectPerfectBtn" style="margin-left: 10px; background-color: #28a745; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">🟢 Perfect Matches</button>
                        <button id="selectGoodBtn" style="margin-left: 5px; background-color: #ffc107; color: black; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">🟡 Good Matches</button>
                        <button id="selectHighConfidenceBtn" style="margin-left: 5px; background-color: #17a2b8; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">High Confidence (70%+)</button>
                        <button id="selectNoneBtn" style="margin-left: 5px; background-color: #6c757d; color: white; padding: 4px 8px; border: none; border-radius: 3px; font-size: 12px;">Select None</button>
                    </div>
                    <div>
                        <strong>Bulk Actions:</strong>
                        <button id="confirmSelectedBtn" style="margin-left: 10px; background-color: #28a745; color: white; padding: 6px 12px; border: none; border-radius: 3px;">✅ Confirm Selected</button>
                        <button id="rejectSelectedBtn" style="margin-left: 5px; background-color: #dc3545; color: white; padding: 6px 12px; border: none; border-radius: 3px;">❌ Reject Selected</button>
                        <button id="phase2BulkConnectBtn" style="margin-left: 5px; background-color: #007bff; color: white; padding: 6px 12px; border: none; border-radius: 3px; display: none;">🔗 Connect Selected</button>
                        <button id="bulkMarkInactiveBtn" style="margin-left: 5px; background-color: #6c757d; color: white; padding: 6px 12px; border: none; border-radius: 3px;">🚫 Mark MPS Inactive</button>
                        <button id="bulkRejectMatchBtn" style="margin-left: 5px; background-color: #ffc107; color: black; padding: 6px 12px; border: none; border-radius: 3px;">❌ Not a Match</button>
                        <span id="selectedCount" style="margin-left: 20px; font-weight: bold;">0 of 0 selected</span>
                    </div>
                </div>
            </div>

            <div class="step-group">
                <h3>🔍 Phase 2: Find New Matches</h3>
                <div class="button-row">
                    <button id="phase2MatchingBtn" class="step-btn">🚀 Find New Matches (Unmatched OSLs)</button>
                    <button id="viewPhase2ReportBtn" class="step-btn">📊 View Phase 2 Report</button>
                </div>
                <div style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                    <div style="margin-bottom: 10px;">
                        <label for="phase2SkipCount" style="font-weight: bold;">Skip Records:</label>
                        <input type="number" id="phase2SkipCount" min="0" max="10000" step="100" value="0" style="width: 80px; margin-left: 10px; margin-right: 20px;">

                        <label for="phase2BatchSize" style="font-weight: bold;">Batch Size:</label>
                        <input type="number" id="phase2BatchSize" min="50" max="1000" step="50" value="300" style="width: 80px; margin-left: 10px; margin-right: 20px;">

                        <label for="phase2MaxCandidates" style="font-weight: bold;">Max Candidates:</label>
                        <select id="phase2MaxCandidates" style="width: 60px; margin-left: 10px; margin-right: 20px;">
                            <option value="1">1</option>
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                        </select>

                        <label for="phase2MinConfidence" style="font-weight: bold;">Min Confidence:</label>
                        <input type="number" id="phase2MinConfidence" min="30" max="100" step="5" value="60" style="width: 60px; margin-left: 10px; margin-right: 10px;">%
                    </div>
                    <span style="font-size: 12px; color: #666;">
                        Skip: OSLs to skip. Batch: OSLs to process. Max Candidates: Show top 1, 2, or 3 matches per OSL. Min Confidence: Minimum score threshold (60% recommended).
                    </span>
                </div>
                <div class="info-box">
                    <p><strong>🔍 Phase 2 Matching:</strong> Finds potential matches for OSLs without vendor_internal_id</p>
                    <p><strong>📊 Configurable Candidates:</strong> Show 1, 2, or 3 best matches per OSL with configurable confidence threshold</p>
                    <p><strong>🎯 50%+ Confidence:</strong> Only shows matches above 50% confidence threshold</p>
                    <p><strong>✅ Same Workflow:</strong> Same validation table with confirm/reject buttons</p>
                    <p><strong>📈 Progress:</strong> Processes 300 OSLs at a time for maximum efficiency</p>
                </div>
            </div>

            <div id="phase2Output" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="b2f" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>🏪 B2F (Back to Front) Workflow</h2>
                <p><strong>Purpose:</strong> Move discs from back stock to front stock for retail display. This process identifies discs that are only in back stock and moves them to a daily B2F location (format: "B2F MM-DD") for physical retrieval and front stock placement.</p>
                <p><strong>Process:</strong> The intelligent auto-select algorithm analyzes MPS release dates to prioritize new releases, considers current stock levels, and applies sales-based logic to determine optimal quantities. Selected discs are assigned to today's B2F location for staff to physically move to front stock.</p>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #e8f5e8; border-left-color: #28a745; padding: 15px; margin-bottom: 20px;">
                    <h3>📊 Current Status</h3>
                    <p><strong>Available for B2F:</strong> <span id="b2fRecordCount">Loading...</span> discs</p>
                    <p><em>Discs currently in back stock with no existing front stock or B2F inventory. These are candidates for moving to front stock.</em></p>
                </div>

                <div class="info-box" style="background: #fff3cd; border-left-color: #ffc107; padding: 15px; margin-bottom: 20px;">
                    <h3>🆕 New Release Priority System</h3>
                    <p><strong>The auto-select algorithm prioritizes new releases to ensure adequate front stock:</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
                        <li><strong>🎯 New Online Releases:</strong> Discs with release_date_online within past month or future get <strong>minimum 5 total stock</strong> (front + B2F)</li>
                        <li><strong>📦 New MPS Records:</strong> Discs with no release date but MPS created within past month get <strong>minimum 3 total stock</strong></li>
                        <li><strong>📈 Smart Stocking:</strong> System considers existing front stock and only adds what's needed to reach targets</li>
                        <li><strong>🔄 Example:</strong> If a new release has 2 discs in front stock, auto-select will add 3 more to reach the 5-disc target</li>
                    </ul>
                </div>

                <div class="button-grid" style="margin-bottom: 20px;">
                    <div class="button-group">
                        <button id="printB2FBtn" class="run-once-btn">🖨️ Print B2F List</button>
                        <small><strong>PDF Generation:</strong> Creates a formatted PDF list of all discs currently assigned to today's B2F location (format: "B2F MM-DD"). Includes disc IDs, G-codes, and titles for easy reference during physical picking.</small>
                    </div>
                    <div class="button-group">
                        <button id="downloadB2FTextBtn" class="run-once-btn">📄 Download Text List</button>
                        <small><strong>Simple Text Export:</strong> Downloads a plain text file containing just the disc IDs (g_pull values) for today's B2F location. Useful for quick scanning or importing into other systems.</small>
                    </div>
                </div>

                <div class="info-box" style="background: #fff3cd; border-left-color: #ffc107; padding: 15px; margin-bottom: 20px;">
                    <h3>🤖 Auto-Select B2Fs</h3>
                    <p><strong>Intelligent Selection Algorithm:</strong> Automatically selects discs for B2F based on sales data, quality grades, and release dates. Uses the following logic:</p>
                    <ul style="margin: 10px 0; padding-left: 20px; font-size: 14px;">
                        <li><strong>New Release Priority:</strong> For discs with release_date_online within past month or future: ensures minimum 5 total stock (front + B2F)</li>
                        <li><strong>New MPS Priority:</strong> For discs with no release date but MPS created within past month: ensures minimum 3 total stock</li>
                        <li><strong>Sales-Based Quantity:</strong> For established discs with sales data, selects the number of discs equal to 30-day sales volume</li>
                        <li><strong>Zero-Sales OSLs:</strong> For OSLs with no recent sales, selects 1 disc to maintain inventory flow</li>
                        <li><strong>Quality Priority:</strong> Prioritizes graded discs (grade > 0) over ungraded discs</li>
                        <li><strong>Age Priority:</strong> Within same grade, selects older discs first (lower disc IDs)</li>
                        <li><strong>Color Variety:</strong> When selecting multiple discs, attempts to get different colors for variety</li>
                    </ul>

                    <div style="display: flex; align-items: center; gap: 15px; margin-top: 15px;">
                        <div>
                            <label for="autoSelectOslLimit" style="display: block; margin-bottom: 5px; font-weight: bold;">Max OSLs to Process:</label>
                            <input type="number" id="autoSelectOslLimit" value="5" min="1" max="50" style="width: 80px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;">
                            <small style="display: block; color: #666; margin-top: 2px;">Limits how many different disc types (OSLs) to process in one run</small>
                        </div>
                        <div>
                            <button id="autoSelectB2FsBtn" class="run-daemon-btn" style="margin-top: 20px;">🎯 Auto-Select B2Fs</button>
                            <small style="display: block; color: #666; margin-top: 5px; max-width: 200px;"><strong>Automated Processing:</strong> Analyzes MPS release dates, current stock levels, and sales data to intelligently select discs. Prioritizes new releases with higher minimum quantities, then applies standard sales-based logic. Shows detailed results including release type and quantity reasoning for each OSL.</small>
                        </div>
                    </div>
                </div>



                <div id="b2fOutput" class="output" style="display: none;"></div>
            </div>
        </div>
    </div>

    <div id="shopify-dupes" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>🛒 Duplicate Shopify Products</h2>
                <p>Find and delete duplicate products with handles ending in d######-1 or d######-2</p>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #fff3cd; border-left-color: #ffc107; padding: 15px; margin-bottom: 20px;">
                    <h3>⚠️ Important</h3>
                    <p>This tool finds products with handles ending in patterns like:</p>
                    <ul>
                        <li><code>d######-1</code> (e.g., d423546-1)</li>
                        <li><code>d######-2</code> (e.g., d423546-2)</li>
                    </ul>
                    <p>These are typically duplicate variants that should be removed from your Shopify store.</p>
                </div>

                <div class="button-grid" style="margin-bottom: 20px;">
                    <div class="button-group">
                        <button id="findDuplicatesBtn" class="run-once-btn">🔍 Find Duplicates</button>
                        <small>Search for duplicate products</small>
                    </div>
                    <div class="button-group">
                        <button id="deleteSelectedBtn" class="run-daemon-btn" style="display: none;">🗑️ Delete Selected</button>
                        <small>Delete checked products</small>
                    </div>
                </div>

                <div id="duplicatesTableContainer" style="display: none;">
                    <h3>Duplicate Products Found</h3>
                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="selectAllDuplicates"> Select All
                        </label>
                        <span id="duplicateCount" style="margin-left: 15px; font-weight: bold;"></span>
                    </div>
                    <div style="max-height: 600px; overflow-y: auto; border: 1px solid #ddd;">
                        <table id="duplicatesTable" style="width: 100%; border-collapse: collapse; font-size: 12px;">
                            <thead style="background-color: #f5f5f5; position: sticky; top: 0;">
                                <tr>
                                    <th style="border: 1px solid #ddd; padding: 8px; width: 50px;">Select</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">Product ID</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">Title</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">Handle</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">Status</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">Created</th>
                                    <th style="border: 1px solid #ddd; padding: 8px;">URL</th>
                                </tr>
                            </thead>
                            <tbody id="duplicatesTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="shopifyDupesOutput" class="output" style="display: none;"></div>
            </div>
        </div>
    </div>

    <div id="documentation" class="tab-content">
        <div class="readme">
            <h2>Documentation</h2>
            <div id="readmeContent"></div>
        </div>
    </div>

    <div id="amazon" class="tab-content">
        <div class="container mt-4">
            <h1>Amazon FBA Data Management</h1>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Amazon SP-API Connection</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Test and manage your Amazon Selling Partner API connection for listing management.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>What this section does:</strong></p>
                        <ul>
                            <li>Tests your Amazon SP-API connection and credentials</li>
                            <li>Allows you to delete Amazon listings via the task queue</li>
                            <li>Manages Amazon marketplace operations</li>
                            <li>Integrates with your existing task queue system</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon SP-API credentials configured in .env file</li>
                            <li>Valid Amazon Seller ID and marketplace configuration</li>
                            <li>Proper Login with Amazon (LWA) tokens</li>
                        </ol>
                    </div>

                    <div class="button-row">
                        <button id="test-amazon-connection" class="btn btn-info">
                            Test Amazon Connection
                        </button>
                    </div>
                    <div id="amazon-connection-result" class="mt-3"></div>

                    <hr class="my-4">

                    <h3>Delete Amazon Listing</h3>
                    <div class="form-group">
                        <label for="amazon-sku">SKU to Delete:</label>
                        <input type="text" id="amazon-sku" placeholder="Enter Amazon SKU" required>
                    </div>
                    <div class="form-group">
                        <label for="amazon-marketplace">Marketplace (optional):</label>
                        <select id="amazon-marketplace">
                            <option value="ATVPDKIKX0DER">US (ATVPDKIKX0DER)</option>
                            <option value="A2EUQ1WTGCTBG2">Canada (A2EUQ1WTGCTBG2)</option>
                            <option value="A1AM78C64UM0Y8">Mexico (A1AM78C64UM0Y8)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="amazon-reason">Reason for Deletion (optional):</label>
                        <input type="text" id="amazon-reason" placeholder="e.g., Discontinued product, inventory error">
                    </div>
                    <button id="delete-amazon-listing" class="btn btn-danger">
                        Enqueue Listing Deletion
                    </button>
                    <div id="amazon-deletion-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Import Monthly Inventory Ledger Summary</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Imports Amazon Inventory Ledger Summary reports (.txt files) into the database.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>IMPORTANT:</strong> Before running the import, make sure you have:</p>
                        <ol>
                            <li>Downloaded the "Monthly Inventory Ledger Summary" report from Amazon Seller Central</li>
                            <li>Saved the file as "Amazon Inventory Ledger Summary YYYY-MM.txt" (e.g., "Amazon Inventory Ledger Summary 2025-04.txt")</li>
                            <li>Placed the file in: <code>C:\Users\<USER>\supabase_project\data\external data</code></li>
                        </ol>
                    </div>

                    <p><strong>What this process does:</strong></p>
                    <ul>
                        <li>Scans the data directory for all Amazon Inventory Ledger Summary files</li>
                        <li>Extracts the date (YYYY-MM) from each filename</li>
                        <li>Imports each file into the <code>it_amaz_monthly_inventory_ledger_summary</code> table</li>
                        <li>Logs the import in the task queue</li>
                    </ul>

                    <p><strong>Note:</strong> This process appends data to the existing table. It does not delete or replace existing records.</p>

                    <button id="run-amazon-import" class="btn btn-primary">
                        Run Amazon FBA Import
                    </button>
                    <div id="amazon-import-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Import Amazon Fulfilled Inventory Report</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Import the Amazon Fulfilled Inventory Report to get current FBA inventory quantities.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>What this import does:</strong></p>
                        <ul>
                            <li>Imports Amazon Fulfilled Inventory Report data (snapshot)</li>
                            <li>Shows current FBA inventory quantities by SKU</li>
                            <li>Truncates and replaces existing data (not historical)</li>
                            <li>Maps seller SKUs to ASINs and fulfillment channel SKUs</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>File Requirements:</strong></p>
                        <ul>
                            <li>File must be named: <code>Amazon Fulfilled Inventory Report.txt</code></li>
                            <li>Place file in: <code>data/external data/</code> folder</li>
                            <li>Tab-separated format with headers</li>
                            <li>Expected columns: seller-sku, fulfillment-channel-sku, asin, condition-type, Warehouse-Condition-code, Quantity Available</li>
                        </ul>
                    </div>

                    <p><strong>Note:</strong> This is a snapshot import that replaces all existing data. Use this to get current FBA inventory levels.</p>

                    <button id="import-fulfilled-inventory" class="btn btn-info">
                        Import Fulfilled Inventory Report
                    </button>
                    <div id="fulfilled-inventory-import-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Refresh FBA Carrying Cost Data</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Refreshes the materialized view that calculates average carrying costs for FBA inventory valuation.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Refreshes the <code>mv_sdasin_avg_carrying_cost_fba</code> materialized view</li>
                            <li>Recalculates average carrying costs for all SDAINs based on current disc data</li>
                            <li>Uses priority logic: unsold discs → FBM sold discs → all sold discs → $5.00 default</li>
                            <li>Updates the cached data used for FBA inventory valuation reports</li>
                        </ul>
                    </div>

                    <p><strong>When to refresh:</strong></p>
                    <ul>
                        <li>After importing new disc data or updating carrying costs</li>
                        <li>Before generating monthly FBA inventory value reports</li>
                        <li>When you notice carrying cost calculations seem outdated</li>
                    </ul>

                    <p><strong>Note:</strong> This process may take a few minutes for large datasets as it recalculates costs for all SDAINs.</p>

                    <button id="refresh-fba-carrying-costs" class="btn btn-success">
                        Refresh FBA Carrying Costs
                    </button>
                    <div id="fba-carrying-costs-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Monthly FBA Inventory Value Report</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Calculates and stores monthly FBA inventory valuation based on Amazon ledger data and carrying costs.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds the most recent month from Amazon Inventory Ledger Summary data</li>
                            <li>Matches Amazon SKUs (MSKU) to your SDAINs via <code>fba_sku</code> field</li>
                            <li>Uses FBA carrying costs from the materialized view</li>
                            <li>Calculates total units, weighted average cost, and total inventory value</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                            <li>Processes data in chunks of 1000 records to handle large datasets</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>After importing new Amazon Inventory Ledger Summary data</li>
                        <li>At month-end to generate inventory valuation reports</li>
                        <li>When you need updated FBA inventory value calculations</li>
                    </ul>

                    <p><strong>Note:</strong> This process handles large datasets by processing records in chunks of 1000. Processing time depends on the amount of data.</p>

                    <button id="generate-fba-inventory-report" class="btn btn-primary">
                        Generate FBA Inventory Report
                    </button>
                    <div id="fba-inventory-report-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Historical FBA Inventory Reports</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Generates FBA inventory value reports for ALL months that have data, not just the latest month.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6f42c1; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds ALL distinct months from Amazon Inventory Ledger Summary data</li>
                            <li>Generates a report for each month that has data</li>
                            <li>Updates existing reports or creates new ones as needed</li>
                            <li>Skips months that are locked (locked_at is not null)</li>
                            <li>Processes each month's data in chunks of 1000 records</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Important Notes:</strong></p>
                        <ul>
                            <li><strong>Locked Reports:</strong> Months with locked_at set will be skipped entirely</li>
                            <li><strong>Large Dataset:</strong> This processes ALL historical data, which may take several minutes</li>
                            <li><strong>Overwrites Data:</strong> Unlocked existing reports will be updated with current calculations</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported for multiple months</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>When you need to backfill historical FBA inventory valuations</li>
                        <li>After importing multiple months of Amazon data</li>
                        <li>When carrying costs have been updated and you want to recalculate all unlocked months</li>
                        <li>For comprehensive historical reporting and analysis</li>
                    </ul>

                    <p><strong>Note:</strong> This process can take several minutes depending on the amount of historical data. Progress will be logged during processing.</p>

                    <button id="generate-historical-fba-reports" class="btn btn-warning">
                        Generate Historical FBA Reports
                    </button>
                    <div id="historical-fba-reports-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Manage FBA Report Locks</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            View, lock, and unlock monthly FBA inventory value reports to control which data can be modified.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin-bottom: 15px;">
                        <p><strong>What this interface does:</strong></p>
                        <ul>
                            <li>Shows all monthly FBA inventory value reports</li>
                            <li>Displays lock status and variance between original and new values</li>
                            <li>Allows you to lock reports to prevent future modifications</li>
                            <li>Allows you to unlock reports (with confirmation) to allow modifications</li>
                            <li>Shows when reports were locked and by whom (if available)</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Lock Management:</strong></p>
                        <ul>
                            <li><strong>Lock:</strong> Prevents the main columns from being updated during report generation</li>
                            <li><strong>Unlock:</strong> Allows the main columns to be updated (requires confirmation)</li>
                            <li><strong>Variance Detection:</strong> Shows if _new columns differ from main columns</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <button id="refresh-fba-locks" class="btn btn-info">
                            Refresh Report List
                        </button>
                    </div>

                    <div id="fba-locks-table-container">
                        <div class="alert alert-info">Click "Refresh Report List" to load the FBA reports.</div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Update Keepa Best Sellers Ranks</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Updates Amazon Best Sellers rank data from Keepa API for sales rank analysis and reporting.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Fetches the top 5,000 best-selling products from Amazon category 3406051 (US domain)</li>
                            <li>Uses Keepa API to get current 30-day average sales rank data</li>
                            <li>Updates <code>t_sdasins.so_rank_30day_avg</code> with current rank position</li>
                            <li>Sets <code>t_sdasins.so_rank_30day_avg_date</code> to current timestamp</li>
                            <li><strong>NEW:</strong> Automatically adds competitor ASINs to your database for market intelligence</li>
                            <li>Creates detailed notes with discovery information and source tracking</li>
                            <li>Processes data in batches of 50 for optimal performance</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>API Details:</strong></p>
                        <ul>
                            <li><strong>API Provider:</strong> Keepa.com</li>
                            <li><strong>Category:</strong> 3406051 (Disc Golf category)</li>
                            <li><strong>Domain:</strong> 1 (US Amazon marketplace)</li>
                            <li><strong>Token Cost:</strong> 50 tokens per request</li>
                            <li><strong>Data Returned:</strong> Up to 5,000 ASINs ordered by sales rank</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ul>
                            <li>Keepa API access key configured in the script</li>
                            <li>Sufficient Keepa API tokens (50 tokens required per run)</li>
                            <li>ASINs in <code>t_sdasins</code> table that match Amazon products</li>
                            <li>Active internet connection for API access</li>
                        </ul>
                    </div>

                    <p><strong>Expected Results:</strong></p>
                    <ul>
                        <li>Updates existing ASINs with their current best sellers rank (1-5000)</li>
                        <li><strong>NEW:</strong> Automatically adds competitor ASINs not in your database as new records</li>
                        <li>New competitor ASINs are marked with detailed notes and source information</li>
                        <li>Rank 1 = #1 best seller, Rank 2 = #2 best seller, etc.</li>
                        <li>Process typically takes 2-3 minutes to complete</li>
                        <li>Detailed progress and competitor discovery shown in the results section below</li>
                    </ul>

                    <p><strong>Note:</strong> This updates sales rank data for competitive analysis and market positioning. Run periodically to track rank changes over time.</p>

                    <button id="update-keepa-ranks" class="btn btn-success">
                        Update Keepa Best Sellers Ranks
                    </button>
                    <div id="keepa-ranks-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate FBA Ship-In Recommendations</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Generates intelligent FBA ship-in quantity recommendations based on Keepa sales rank data and current inventory levels.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Analyzes all FBA-enabled products in <code>t_sdasins</code> with sales rank data</li>
                            <li>Identifies products with low/zero inventory or missing ship-in recommendations</li>
                            <li>Calculates optimal ship-in quantities based on Amazon Best Sellers rank performance</li>
                            <li>Creates/updates records in <code>import_table_amaz_fba_inv_rpt</code> table</li>
                            <li>Processes data in chunks of 1,000 records for optimal performance</li>
                            <li>Uses intelligent rank-based quantity algorithms for inventory optimization</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Recommendation Logic:</strong></p>
                        <ul>
                            <li><strong>Rank &lt; 300:</strong> 5 units (Top performers - high demand)</li>
                            <li><strong>Rank &lt; 600:</strong> 4 units (Strong performers - good demand)</li>
                            <li><strong>Rank &lt; 1000:</strong> 3 units (Solid performers - moderate demand)</li>
                            <li><strong>Rank &lt; 1500:</strong> 2 units (Steady performers - consistent demand)</li>
                            <li><strong>Rank &lt; 2130:</strong> 1 unit (Lower performers - minimal stock)</li>
                            <li><strong>Rank &gt; 2129:</strong> Skip (Poor performers - no recommendation)</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Target Criteria:</strong></p>
                        <ul>
                            <li>Products with <code>fba = 'Y'</code> and valid <code>fbafnsku</code></li>
                            <li>Products with current Keepa sales rank data (<code>so_rank_30day_avg</code>)</li>
                            <li>Products with zero/null available inventory OR missing ship-in recommendations</li>
                            <li>Excludes products with ranks above 2,129 (poor performers)</li>
                        </ul>
                    </div>

                    <p><strong>Expected Results:</strong></p>
                    <ul>
                        <li>Creates new FBA inventory records for products without existing entries</li>
                        <li>Updates existing records that meet the low-inventory criteria</li>
                        <li>Sets appropriate ship-in quantities based on sales performance</li>
                        <li>Typically processes 2,000+ FBA products in 1-2 minutes</li>
                        <li>Provides detailed summary of created/updated recommendations</li>
                    </ul>

                    <p><strong>Note:</strong> Run this after updating Keepa ranks to ensure recommendations are based on the latest sales performance data. This helps optimize FBA inventory levels and prevent stockouts on high-performing products.</p>

                    <button id="generate-fba-recommendations" class="btn btn-primary">
                        Generate FBA Ship-In Recommendations
                    </button>
                    <div id="fba-recommendations-result" class="mt-3"></div>
                </div>
            </div>

            <!-- Reset FBM Inactive Records Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3>🔄 Reset FBM Inactive Records</h3>
                </div>
                <div class="card-body">
                    <p><strong>Purpose:</strong> Reset FBM records for inactive MPS products with zero inventory.</p>

                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6c757d; margin-bottom: 15px;">
                        <p><strong>What this script does:</strong></p>
                        <ul>
                            <li>Queries the <code>v_sdasins_fbm_inv0_mps_inactive</code> view for relevant records</li>
                            <li>Updates corresponding <code>t_sdasins</code> records with the following changes:</li>
                            <ul>
                                <li>Sets <code>fbm_uploaded_at</code> to <strong>NULL</strong></li>
                                <li>Sets <code>min_weight</code> to <strong>1</strong></li>
                                <li>Sets <code>max_weight</code> to <strong>2</strong></li>
                            </ul>
                            <li>Provides detailed feedback on records found and updated</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Use Case:</strong></p>
                        <p>This script is designed to reset FBM (Fulfilled by Merchant) records for products that are no longer active or have zero inventory. By resetting these fields, the products can be re-processed through the normal workflow when they become available again.</p>
                    </div>

                    <div class="form-group" style="background-color: #d1ecf1; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>Expected Results:</strong></p>
                        <ul>
                            <li>Clears FBM upload timestamps for inactive products</li>
                            <li>Standardizes weight ranges to default values (1-2)</li>
                            <li>Allows products to be re-evaluated for FBM eligibility</li>
                            <li>Provides count of records processed</li>
                        </ul>
                    </div>

                    <p><strong>Note:</strong> This operation is safe to run multiple times. Only records matching the view criteria will be updated.</p>

                    <button id="reset-fbm-inactive-records" class="btn btn-warning">
                        🔄 Reset FBM Inactive Records
                    </button>
                    <div id="reset-fbm-inactive-result" class="mt-3"></div>
                </div>
            </div>

            <!-- Reset FBA Inactive Records Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3>🚫 Reset FBA Inactive Records</h3>
                </div>
                <div class="card-body">
                    <p><strong>Purpose:</strong> Reset FBA records for inactive MPS products with zero inventory in both local and Amazon systems.</p>

                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6c757d; margin-bottom: 15px;">
                        <p><strong>What this script does:</strong></p>
                        <ul>
                            <li>Queries the <code>v_sdasins_fba_inv0_mps_inactive</code> view for relevant records</li>
                            <li>Updates corresponding <code>t_sdasins</code> records with the following changes:</li>
                            <ul>
                                <li>Sets <code>fba</code> to <strong>"N"</strong></li>
                                <li>Sets <code>fbafnsku</code> to <strong>NULL</strong></li>
                                <li>Sets <code>fba_uploaded_at</code> to <strong>NULL</strong></li>
                                <li>Sets <code>min_weight</code> to <strong>1</strong></li>
                                <li>Sets <code>max_weight</code> to <strong>2</strong></li>
                                <li>Prepends <code>notes</code> with discontinuation message and timestamp</li>
                            </ul>
                            <li>Provides detailed feedback on records found and updated</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #f8d7da; padding: 10px; border-left: 4px solid #dc3545; margin-bottom: 15px;">
                        <p><strong>Use Case:</strong></p>
                        <p>This script is designed to remove FBA (Fulfilled by Amazon) listings for products that are discontinued and have zero inventory both locally and on Amazon. It marks them as inactive and adds a note explaining when and why they were removed.</p>
                    </div>

                    <div class="form-group" style="background-color: #d1ecf1; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>Expected Results:</strong></p>
                        <ul>
                            <li>Disables FBA fulfillment for discontinued products</li>
                            <li>Clears FBA-specific identifiers and timestamps</li>
                            <li>Standardizes weight ranges to default values (1-2)</li>
                            <li>Documents the removal with timestamped notes</li>
                            <li>Prevents products from being re-uploaded to FBA automatically</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>⚠️ Important:</strong></p>
                        <p>This operation removes products from Amazon FBA. Only run this for products that are truly discontinued and should no longer be sold on Amazon. The notes field will be updated with a permanent record of this action.</p>
                    </div>

                    <p><strong>Note:</strong> This operation is safe to run multiple times. Only records matching the view criteria will be updated.</p>

                    <button id="reset-fba-inactive-records" class="btn btn-danger">
                        🚫 Reset FBA Inactive Records
                    </button>
                    <div id="reset-fba-inactive-result" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>



    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');

                // Special handling for ToDo tab - open in new window
                if (tabId === 'todo') {
                    window.open('ToDo.html', '_blank');
                    return;
                }

                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tabId).classList.add('active');

                // Load data for specific tabs
                if (tabId === 'dashboard') {
                    refreshStatus();
                } else if (tabId === 'worker') {
                    refreshWorkerStatus();
                } else if (tabId === 'informed') {
                    refreshInformedStatus();
                } else if (tabId === 'b2f') {
                    loadB2FCount();
                } else if (tabId === 'shopify-dupes') {
                    // Initialize the shopify dupes tab
                    document.getElementById('duplicatesTableContainer').style.display = 'none';
                    document.getElementById('deleteSelectedBtn').style.display = 'none';
                } else if (tabId === 'discraft') {
                    refreshDiscraftStatus();
                    loadDiscraftSchedulerStatus();
                    // Load current threshold from server
                    fetch('/api/discraft/scheduler/threshold')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.threshold) {
                                document.getElementById('exportThreshold').value = data.threshold;
                            }
                        })
                        .catch(error => console.log('Could not load threshold:', error));
                } else if (tabId === 'amazon') {
                    refreshFbaLocksList();
                }

            });
        });

        // Fetch README content
        fetch('README.md')
            .then(response => response.text())
            .then(text => {
                // Simple markdown to HTML conversion (very basic)
                let html = text
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/```sql([\s\S]*?)```/g, '<pre><code class="language-sql">$1</code></pre>')
                    .replace(/```bash([\s\S]*?)```/g, '<pre><code class="language-bash">$1</code></pre>')
                    .replace(/```json([\s\S]*?)```/g, '<pre><code class="language-json">$1</code></pre>')
                    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                    .replace(/^\d\. (.*$)/gm, '<ol><li>$1</li></ol>')
                    .replace(/<\/ol><ol>/g, '');

                document.getElementById('readmeContent').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('readmeContent').innerHTML = '<p>Error loading README: ' + error.message + '</p>';
            });

        // Enqueue task form submission
        document.getElementById('enqueueForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const imageId = document.getElementById('imageId').value;
            const delayMinutes = document.getElementById('delayMinutes').value || 0;

            const outputDiv = document.getElementById('enqueueOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enqueueing task for image ID ' + imageId + ' with delay ' + delayMinutes + ' minutes...\n';

            // Call API to enqueue task
            fetch('/api/enqueue-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ imageId, delayMinutes }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Task enqueued successfully!\n';
                    if (data.details && data.details.output) {
                        outputDiv.innerHTML += data.details.output + '\n';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });

            // Refresh task counts after a short delay
            setTimeout(refreshStatus, 2000);
        });



        // View tasks button
        document.getElementById('viewTasksBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('tasksOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading tasks...\n';

            // Call API to get tasks
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    outputDiv.innerHTML = '';

                    if (data.pendingTasks && data.pendingTasks.length > 0) {
                        outputDiv.innerHTML += 'Pending Tasks Ready to Process:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.pendingTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No pending tasks ready to process.\n\n';
                    }

                    if (data.futureTasks && data.futureTasks.length > 0) {
                        outputDiv.innerHTML += 'Future Scheduled Tasks:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.futureTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No future scheduled tasks.\n\n';
                    }

                    if (data.completedTasks && data.completedTasks.length > 0) {
                        outputDiv.innerHTML += 'Recently Completed Tasks:\n\n';

                        // Format completed tasks to highlight success messages
                        data.completedTasks.forEach(task => {
                            outputDiv.innerHTML += `Task ID: ${task.id}\n`;
                            outputDiv.innerHTML += `Processed At: ${task.processed_at}\n`;

                            if (task.result && task.result.message) {
                                const messageClass = task.result.message.includes('Success!') ? 'success-message' : 'failure-message';
                                outputDiv.innerHTML += `Result: <span class="${messageClass}">${task.result.message}</span>\n`;
                            } else if (task.result) {
                                outputDiv.innerHTML += `Result: ${JSON.stringify(task.result)}\n`;
                            }

                            outputDiv.innerHTML += '\n';
                        });
                    } else {
                        outputDiv.innerHTML += 'No recently completed tasks.\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = 'Error loading tasks: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
        });

        // Run worker button
        document.getElementById('runWorkerBtn').addEventListener('click', function() {
            const mode = document.getElementById('workerMode').value;
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = ''; // Clear previous output

            if (mode === 'once') {
                outputDiv.innerHTML = 'Running worker once...\n';

                // Call API to run worker once
                fetch('/api/worker/run-once', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker started successfully.\n';
                        outputDiv.innerHTML += 'The worker will process all available tasks and then exit.\n';
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }

                    // Refresh status after a short delay
                    setTimeout(refreshStatus, 1000);
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            } else {
                outputDiv.innerHTML = 'Starting worker daemon...\n';
                outputDiv.innerHTML += 'The worker will run continuously, checking for new tasks every 15 seconds and processing up to 100 tasks per run.\n';

                // Call API to start worker daemon
                fetch('/api/worker/start-daemon', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker daemon started successfully!\n';
                        outputDiv.innerHTML += 'The worker will continue running until stopped.\n';

                        // Update status indicators and buttons
                        updateWorkerStatusIndicators('running');

                        // Start polling for status updates
                        startStatusPolling();
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            }
        });

        // Stop worker button
        document.getElementById('stopWorkerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML += '\nStopping worker daemon...\n';

            // Call API to stop worker daemon
            fetch('/api/worker/stop-daemon', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status indicators and buttons
                    updateWorkerStatusIndicators('stopped');

                    outputDiv.innerHTML += 'Worker daemon stopped successfully.\n';
                    outputDiv.innerHTML += 'All pending tasks will remain in the queue until the worker is started again.\n';

                    // Stop polling for status updates
                    stopStatusPolling();
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Refresh status button
        document.getElementById('refreshStatus').addEventListener('click', refreshStatus);

        // Function to refresh status
        function refreshStatus() {
            // First, fetch worker status
            fetch('/api/worker/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('pendingTasksCount').textContent = data.pendingTasksCount || 0;
                    document.getElementById('futureTasksCount').textContent = data.futureTasksCount || 0;

                    if (data.lastRunTime) {
                        const formattedTime = new Date(data.lastRunTime).toLocaleString();
                        document.getElementById('lastRunTime').textContent = formattedTime;
                        document.getElementById('workerTabLastRunTime').textContent = formattedTime;
                    } else {
                        document.getElementById('lastRunTime').textContent = 'Never';
                        document.getElementById('workerTabLastRunTime').textContent = 'Never';
                    }

                    // Update worker status indicators (both in dashboard and worker tab)
                    updateWorkerStatusIndicators(data.status);

                    // Update worker output if available
                    if (data.output && data.output.length > 0) {
                        // Show the output div in the worker tab
                        const outputDiv = document.getElementById('workerOutput');
                        outputDiv.style.display = 'block';

                        // Update both console logs
                        updateConsoleLog(data.output);
                    }
                })
                .catch(error => {
                    console.error('Error fetching worker status:', error);
                });

            // Then, directly fetch task type breakdown (don't wait for worker status)
            fetch('/api/tasks/by-type')
                .then(response => response.json())
                .then(data => {
                    if (!data.taskTypes) {
                        const tableBody = document.getElementById('taskTableBody');
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No task data available.</td></tr>';
                        return;
                    }

                    // Update the workflow table
                    updateWorkflowTable(
                        data.taskTypes.pending || {},
                        data.taskTypes.future || {},
                        data.taskTypes.completed || {},
                        data.taskTypes.error || {}
                    );
                })
                .catch(error => {
                    console.error('Error fetching task type breakdown:', error);
                    const tableBody = document.getElementById('taskTableBody');
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading task statistics. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        // Function to update worker status indicators
        function updateWorkerStatusIndicators(status) {
            const statusIndicators = document.querySelectorAll('.status-indicator');

            if (status === 'running') {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-stopped');
                    indicator.classList.add('status-running');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Running';
                document.getElementById('workerTabStatus').textContent = 'Running';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'none';
                document.getElementById('stopWorkerBtn').style.display = 'inline-block';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'none';
                document.getElementById('dashboardRunWorkerDaemon').style.display = 'none';
            } else {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-running');
                    indicator.classList.add('status-stopped');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Stopped';
                document.getElementById('workerTabStatus').textContent = 'Stopped';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'inline-block';
                document.getElementById('stopWorkerBtn').style.display = 'none';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'inline-block';
                document.getElementById('dashboardRunWorkerDaemon').style.display = 'inline-block';
            }
        }

        // Function to update console log
        function updateConsoleLog(output) {
            if (!output || output.length === 0) return;

            const dashboardConsoleLog = document.getElementById('dashboardConsoleLog');
            const workerOutput = document.getElementById('workerOutput');
            const showTimestamps = document.getElementById('showTimestampsCheckbox').checked;
            const filterType = document.getElementById('logFilterSelect').value;
            const taskTypeFilter = document.getElementById('taskTypeFilter').value.toLowerCase();

            // Parse and format the log entries
            let formattedOutput = '';

            output.forEach(line => {
                // Skip empty lines
                if (!line.trim()) return;

                // Add timestamp
                const timestamp = new Date().toLocaleTimeString();

                // Parse the line to extract task information
                let formattedLine = '';
                let logClass = '';

                // Check if this is a task-related log
                const taskIdMatch = line.match(/\[taskQueueWorker\.js\] Processing task (\d+) of type ([\w_]+)/);
                const taskCompletedMatch = line.match(/\[taskQueueWorker\.js\] Successfully (completed|updated|enqueued|matched|generated|processed)/);
                const taskErrorMatch = line.match(/\[taskQueueWorker\.js\] (Error|Exception|Failed)/);
                const taskProcessingMatch = line.match(/\[taskQueueWorker\.js\] (Processing|Fetching|Calculating|Finding|Updating)/);

                // Apply filters
                if (filterType !== 'all') {
                    if (filterType === 'error' && !taskErrorMatch) return;
                    if (filterType === 'success' && !taskCompletedMatch) return;
                    if (filterType === 'processing' && !taskProcessingMatch && !taskIdMatch) return;
                }

                // Apply task type filter
                if (taskTypeFilter && taskIdMatch) {
                    const taskType = taskIdMatch[2].toLowerCase();
                    if (!taskType.includes(taskTypeFilter)) return;
                }

                // Format based on content
                if (taskIdMatch) {
                    const taskId = taskIdMatch[1];
                    const taskType = taskIdMatch[2];

                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="task-id">Task #${taskId}</span>`;
                    formattedLine += `<span class="task-type">${taskType}</span>`;
                    formattedLine += `<span>Processing started</span></div>`;
                    logClass = 'processing';
                } else if (taskCompletedMatch) {
                    formattedLine = `<div class="log-entry completed">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'success';
                } else if (taskErrorMatch) {
                    formattedLine = `<div class="log-entry error">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'error';
                } else if (taskProcessingMatch) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="processing">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else if (line.includes('[taskQueueWorker.js]')) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="info">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line;
                    formattedLine += `</div>`;
                }

                formattedOutput += formattedLine;
            });

            // Update both console logs
            dashboardConsoleLog.innerHTML = formattedOutput || '<div class="log-entry info">No log entries match the current filters</div>';

            // Auto-scroll if enabled
            if (document.getElementById('autoScrollCheckbox').checked) {
                dashboardConsoleLog.scrollTop = dashboardConsoleLog.scrollHeight;
                if (workerOutput.style.display !== 'none') {
                    workerOutput.scrollTop = workerOutput.scrollHeight;
                }
            }
        }

        // Function to update the workflow table
        function updateWorkflowTable(pendingTasks, futureTasks, completedTasks, errorTasks) {
            // Define the workflow order of task types
            const workflowOrder = [
                // Image verification workflow
                'clear_disc_verification',
                'verify_disc_image',
                'check_if_disc_is_ready',
                'check_if_disc_ready_to_publish',
                'publish_disc',
                'publish_product_disc',

                // Field generation tasks
                'generate_disc_title_pull_and_handle',
                'generate_mps_fields',

                // Matching and inventory tasks
                'set_disc_carry_cost',
                'match_disc_to_asins',
                'match_disc_to_osl',
                'mps_price_verified_try_upload_osls',
                'mps_price_verified_osl_uploaded_look_for_discs',
                'toggle_osl_ready_button',
                'plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload',
                'reconcile_clear_count_from_shopify_for_sold_disc',
                'update_osl_after_publish',

                // Legacy image tasks
                'verify_t_images_image',
                'insert_new_t_images_record',
                'delete_t_images_record'
            ];

            const tableBody = document.getElementById('taskTableBody');
            tableBody.innerHTML = '';

            // Create a row for each task type in workflow order
            workflowOrder.forEach(taskType => {
                const pendingCount = pendingTasks[taskType] || 0;
                const futureCount = futureTasks[taskType] || 0;
                const completedCount = completedTasks[taskType] || 0;
                const errorCount = errorTasks[taskType] || 0;

                const row = document.createElement('tr');

                // Task name cell
                const nameCell = document.createElement('td');
                nameCell.className = 'task-name';
                nameCell.textContent = taskType;
                row.appendChild(nameCell);

                // Pending count cell
                const pendingCell = document.createElement('td');
                pendingCell.className = 'count ' + (pendingCount > 0 ? 'count-nonzero' : 'count-zero');
                pendingCell.textContent = pendingCount;
                row.appendChild(pendingCell);

                // Future count cell
                const futureCell = document.createElement('td');
                futureCell.className = 'count ' + (futureCount > 0 ? 'count-nonzero' : 'count-zero');
                futureCell.textContent = futureCount;
                row.appendChild(futureCell);

                // Completed count cell
                const completedCell = document.createElement('td');
                completedCell.className = 'count ' + (completedCount > 0 ? 'count-success' : 'count-zero');
                completedCell.textContent = completedCount;
                row.appendChild(completedCell);

                // Error count cell
                const errorCell = document.createElement('td');
                errorCell.className = 'count ' + (errorCount > 0 ? 'count-error' : 'count-zero');
                errorCell.textContent = errorCount;
                row.appendChild(errorCell);

                tableBody.appendChild(row);
            });

            // Show the table
            document.getElementById('taskWorkflowTable').style.display = 'block';
        }

        // Status polling
        let statusPollInterval = null;

        function startStatusPolling() {
            if (!statusPollInterval) {
                statusPollInterval = setInterval(refreshStatus, 5000);
            }
        }

        function stopStatusPolling() {
            if (statusPollInterval) {
                clearInterval(statusPollInterval);
                statusPollInterval = null;
            }
        }

        // Dashboard Run Worker Once button
        document.getElementById('dashboardRunWorkerOnce').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running worker once from dashboard...';

            // Call API to run worker once
            fetch('/api/worker/run-once', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '\nWorker started successfully. Check the Worker tab for output.\n';

                    // Update last run time
                    document.getElementById('lastRunTime').textContent = new Date().toLocaleString();

                    // Refresh status after a short delay to allow worker to process tasks
                    setTimeout(refreshStatus, 2000);
                } else {
                    outputDiv.innerHTML += '\nError: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '\nError: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Dashboard Run Worker as Daemon button
        document.getElementById('dashboardRunWorkerDaemon').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting worker daemon from dashboard...\n';
            outputDiv.innerHTML += 'The worker will run continuously, checking for new tasks every 15 seconds and processing up to 100 tasks per run.\n';

            // Call API to start worker daemon
            fetch('/api/worker/start-daemon', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Worker daemon started successfully!\n';
                    outputDiv.innerHTML += 'The worker will continue running until stopped. Check the Worker tab for live output.\n';

                    // Update status indicators and buttons
                    updateWorkerStatusIndicators('running');

                    // Start polling for status updates
                    startStatusPolling();
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initial status refresh
            refreshStatus();

            // Start polling for status updates
            startStatusPolling();

            // Initial status refresh for Informed tab
            refreshInformedStatus();

            // Initial status refresh for Discraft tab
            refreshDiscraftStatus();

            // B2F Tab Event Listeners - Add safely after DOM is loaded
            try {
                const printBtn = document.getElementById('printB2FBtn');
                const downloadTextBtn = document.getElementById('downloadB2FTextBtn');
                const autoSelectBtn = document.getElementById('autoSelectB2FsBtn');

                if (printBtn) {
                    printBtn.addEventListener('click', printB2FList);
                }

                if (downloadTextBtn) {
                    downloadTextBtn.addEventListener('click', downloadB2FTextList);
                }

                if (autoSelectBtn) {
                    autoSelectBtn.addEventListener('click', autoSelectB2Fs);
                }
            } catch (error) {
                console.log('B2F elements not found, skipping B2F event listeners:', error);
            }
        });

        // Clear console button
        document.getElementById('clearConsoleBtn').addEventListener('click', function() {
            document.getElementById('dashboardConsoleLog').innerHTML = '';
        });

        // Log filter controls
        document.getElementById('logFilterSelect').addEventListener('change', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('taskTypeFilter').addEventListener('input', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('showTimestampsCheckbox').addEventListener('change', function() {
            // Re-apply the current output with the new timestamp setting
            updateConsoleLog(workerOutput);
        });

        // Import Veeqo Sellables button
        document.getElementById('importVeeqoSellablesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importVeeqoSellablesOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating imported_table_veeqo_sellables_export table and running import_veeqo_sellables.js...';

            // Call API to import Veeqo sellables
            fetch('/api/import-veeqo-sellables', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo sellables imported successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Delete Wrong Veeqo Records button
        document.getElementById('deleteWrongVeeqoRecordsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('deleteWrongVeeqoRecordsOutput');
            outputDiv.style.display = 'block';

            // Get the limit value
            const limitSelect = document.getElementById('deleteVeeqoLimit');
            const limit = limitSelect.value;

            // Update the message based on the limit
            let message = 'Identifying and deleting';
            if (limit === '1') {
                message += ' 1 Veeqo record';
            } else if (limit === '10') {
                message += ' 10 Veeqo records';
            } else {
                message += ' all Veeqo records';
            }
            message += ' with variant titles containing " (D#"...';

            outputDiv.innerHTML = message;

            // Confirm before proceeding
            let confirmMessage = 'This will permanently delete ';
            if (limit === '1') {
                confirmMessage += '1 Veeqo product variant';
            } else if (limit === '10') {
                confirmMessage += '10 Veeqo product variants';
            } else {
                confirmMessage += 'ALL Veeqo product variants';
            }
            confirmMessage += ' with variant titles containing " (D#". This action cannot be undone. Are you sure you want to proceed?';

            if (!confirm(confirmMessage)) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to delete wrong Veeqo records
            fetch('/api/delete-wrong-veeqo-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ limit: limit ? parseInt(limit) : null })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Wrong Veeqo records deleted successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nDeletion Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful deletions: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed deletions: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile Discs to Veeqo button
        document.getElementById('reconcileDToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileDToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running reconcileDToVeeqo.js to update discs with inventory discrepancies...';

            // Call API to run reconcileDToVeeqo.js
            fetch('/api/reconcile-d-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Disc to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Enqueue Sold Discs Shopify Tasks button
        document.getElementById('enqueueSoldDiscsShopifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('enqueueSoldDiscsShopifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding sold discs still showing on Shopify and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/enqueue-sold-discs-shopify-tasks', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        data.tasks.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, Disc ID: ${task.disc_id}\n`;
                        });
                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile RPRO to Veeqo button
        document.getElementById('reconcileRproToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileRproToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Refreshing RPRO to Veeqo reconciliation data...';

            // Call API to refresh reconciliation data
            fetch('/api/reconcile-rpro-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'RPRO to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.stats) {
                        outputDiv.innerHTML += '\nReconciliation Statistics:\n';
                        outputDiv.innerHTML += `- Total records: ${data.stats.totalCount}\n`;
                        outputDiv.innerHTML += `- Records with discrepancies: ${data.stats.discrepancyCount}\n`;
                        outputDiv.innerHTML += `- Records where RPRO has more: ${data.stats.rproMoreCount}\n`;
                        outputDiv.innerHTML += `- Records where Veeqo has more: ${data.stats.veeqoMoreCount}\n`;
                        outputDiv.innerHTML += `- Records with matching quantities: ${data.stats.matchingCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Update Veeqo from RPRO button
        document.getElementById('updateVeeqoFromRproBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('updateVeeqoFromRproOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating Veeqo quantities from RPRO data...';

            // Confirm before proceeding
            if (!confirm('This will update Veeqo quantities to match RPRO quantities for all records with discrepancies. Are you sure you want to proceed?')) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to update Veeqo quantities
            fetch('/api/update-veeqo-from-rpro', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo quantities updated successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nUpdate Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful updates: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed updates: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSL Stats button
        document.getElementById('reconcileOslStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOslStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_osl records with correct quantity counts from v_stats_by_osl...';

            // Call API to reconcile OSL stats
            fetch('/api/reconcile-osl-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'OSL stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Amazon Active Listings Report button
        document.getElementById('importAmazonActiveListingsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importAmazonActiveListingsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting Amazon Active Listings Report import...\n';

            // Call API to import Amazon Active Listings Report
            fetch('/api/import-amazon-active-listings', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Amazon Active Listings Report import completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nImport Summary:\n';
                        outputDiv.innerHTML += `- File imported: ${data.summary.fileName || 'Unknown'}\n`;
                        outputDiv.innerHTML += `- Report date: ${data.summary.reportDate || 'Unknown'}\n`;
                        outputDiv.innerHTML += `- Total records imported: ${data.summary.totalRecords || 'Unknown'}\n`;
                        outputDiv.innerHTML += `- Import duration: ${data.summary.duration || 'Unknown'}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import RPRO Data button
        document.getElementById('importRproDataBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importRproDataOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting RPRO data import from R:\\Rpro\\BRIDGE\\invdb.dbf...\n';

            // Call API to import RPRO data
            fetch('/api/import-rpro-data', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'RPRO data import completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nImport Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalRecords || 'Unknown'}\n`;
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches || 'Unknown'}\n`;
                        outputDiv.innerHTML += `- Import duration: ${data.summary.duration || 'Unknown'}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile SDAsin Stats button
        document.getElementById('reconcileSdasinStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileSdasinStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_sdasin records with correct quantity counts from v_stats_by_sdasin...';

            // Call API to reconcile SDAsin stats
            fetch('/api/reconcile-sdasin-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'SDAsin stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Shopify Matrixify Export button
        document.getElementById('importShopifyMatrixifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importShopifyMatrixifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing Shopify Matrixify export data to imported_table_shopify_products_dz...';

            // Call API to import Shopify Matrixify export
            fetch('/api/import-shopify-matrixify', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Shopify Matrixify export imported successfully to imported_table_shopify_products_dz.<br>';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.details.stdout + '</pre>';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + data.error + '<br>';
                    if (data.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.stdout + '</pre>';
                    }
                    if (data.stderr) {
                        outputDiv.innerHTML += '<pre class="error">' + data.stderr + '</pre>';
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSLs to Veeqo button
        document.getElementById('reconcileOSLToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOSLToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding OSLs with inventory discrepancies in Veeqo and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/reconcile-osl-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Records skipped (missing available_quantity): ${data.summary.skippedRecords}\n`;
                        outputDiv.innerHTML += `- Tasks enqueued: ${data.summary.tasksEnqueued}\n`;
                    }

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile SDAsins to Veeqo button
        document.getElementById('reconcileSdasinToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileSdasinToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding SDAsins with quantity discrepancies in Veeqo and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/reconcile-sdasin-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Tasks enqueued: ${data.summary.tasksEnqueued}\n`;
                    }

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, SDAsin ID: ${task.sdasin_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Discs from Google Sheets form
        document.getElementById('importDiscsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const googleSheetsUrl = document.getElementById('googleSheetsUrl').value;
            const validateOnly = document.getElementById('validateOnly').checked;
            const outputDiv = document.getElementById('importDiscsOutput');
            const submitButton = e.target.querySelector('button[type="submit"]');

            // Disable the submit button to prevent multiple submissions
            submitButton.disabled = true;
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = validateOnly ? 'Validating...' : 'Importing...';

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = validateOnly ? 'Validating disc data from Google Sheets...' : 'Importing disc data from Google Sheets...';

            // Call API to import discs
            fetch('/api/import-discs-from-sheets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    googleSheetsUrl: googleSheetsUrl,
                    validateOnly: validateOnly
                }),
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;

                if (data.success) {
                    outputDiv.innerHTML = data.message + '<br>';

                    if (data.validationResults) {
                        outputDiv.innerHTML += '<h4>Validation Results:</h4>';
                        outputDiv.innerHTML += `<p>Total records: ${data.validationResults.totalRecords}</p>`;
                        outputDiv.innerHTML += `<p>Valid records: ${data.validationResults.validRecords}</p>`;
                        outputDiv.innerHTML += `<p>Invalid records: ${data.validationResults.invalidRecords}</p>`;
                        outputDiv.innerHTML += `<p>Empty records (skipped): ${data.validationResults.emptyRecords || 0}</p>`;

                        if (data.validationResults.errors && data.validationResults.errors.length > 0) {
                            outputDiv.innerHTML += '<h5>Validation Errors:</h5><ul>';
                            data.validationResults.errors.forEach(error => {
                                outputDiv.innerHTML += `<li>Row ${error.row}: ${error.message}</li>`;
                            });
                            outputDiv.innerHTML += '</ul>';
                        }
                    }

                    if (data.importResults && !validateOnly) {
                        outputDiv.innerHTML += '<h4>Import Results:</h4>';
                        outputDiv.innerHTML += `<p>Records imported: ${data.importResults.recordsImported}</p>`;
                        outputDiv.innerHTML += '<h5>New IDs in Original Sheet Order (copy these back to your Google Sheet):</h5>';

                        if (data.importResults.newIds && data.importResults.newIds.length > 0) {
                            // Filter out empty/null IDs and display only the actual new IDs
                            const actualNewIds = data.importResults.newIds.filter(id => id !== null && id !== undefined && id !== '');
                            actualNewIds.forEach(id => {
                                outputDiv.innerHTML += `${id}<br>`;
                            });
                        }
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '<br>';
                    if (data.details) {
                        outputDiv.innerHTML += 'Details: ' + data.details + '<br>';
                    }
                }
            })
            .catch(error => {
                // Re-enable the submit button on error
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;

                outputDiv.innerHTML = 'Error: ' + error.message + '<br>';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });



        // Generate OSL Fields for Null G_Code button
        document.getElementById('enqueueGenerateOslFieldsBtn').addEventListener('click', function() {
            const batchSize = document.getElementById('oslBatchSize').value || 100;
            const outputDiv = document.getElementById('enqueueGenerateOslFieldsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Finding OSLs with null g_code and enqueueing tasks (batch size: ${batchSize})...`;

            // Call API to enqueue tasks
            fetch('/api/enqueue-generate-osl-fields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ batchSize }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.task_id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks using the task queue worker.\n';
                    } else {
                        outputDiv.innerHTML += '\nNo OSLs with null g_code were found.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // No need for this anymore as we call refreshStatus() on DOMContentLoaded

        // Informed tab functionality

        // Function to refresh Informed report status
        function refreshInformedStatus() {
            const statusDiv = document.getElementById('informedReportStatus');

            // Call API to get report status
            fetch('/api/informed/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<table class="task-table" style="width: 100%;">';
                        statusHtml += '<thead><tr><th>Report</th><th>Last Updated</th><th>Record Count</th><th>Status</th></tr></thead>';
                        statusHtml += '<tbody>';

                        // Add row for each report
                        data.reports.forEach(report => {
                            const statusClass = report.status === 'OK' ? 'success-message' : 'failure-message';
                            const formattedDate = report.lastUpdated ? new Date(report.lastUpdated).toLocaleString() : 'Never';

                            statusHtml += `<tr>
                                <td>${report.name}</td>
                                <td>${formattedDate}</td>
                                <td>${report.recordCount || 0}</td>
                                <td class="${statusClass}">${report.status}</td>
                            </tr>`;
                        });

                        statusHtml += '</tbody></table>';
                        statusDiv.innerHTML = statusHtml;
                    } else {
                        statusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get report status'}</p>`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });

            // Call API to get scheduler status
            const schedulerStatusDiv = document.getElementById('informedSchedulerStatus');

            fetch('/api/informed/scheduler-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<p>';

                        if (data.enabled) {
                            statusHtml += '<span class="success-message">✓ Scheduler is enabled</span><br>';
                            statusHtml += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                            statusHtml += `Schedule: ${data.schedule}`;
                        } else {
                            statusHtml += '<span class="failure-message">✗ Scheduler is disabled</span>';
                        }

                        statusHtml += '</p>';
                        schedulerStatusDiv.innerHTML = statusHtml;

                        // Update button visibility
                        document.getElementById('enableInformedSchedulerBtn').style.display = data.enabled ? 'none' : 'inline-block';
                        document.getElementById('disableInformedSchedulerBtn').style.display = data.enabled ? 'inline-block' : 'none';
                    } else {
                        schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get scheduler status'}</p>`;
                    }
                })
                .catch(error => {
                    schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });
        }

        // Download Informed Reports button
        document.getElementById('downloadInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('downloadInformedReportsOutput');
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Downloading reports from Informed Repricer (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="downloadProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('downloadProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during download
            const downloadBtn = document.getElementById('downloadInformedReportsBtn');
            downloadBtn.disabled = true;
            downloadBtn.innerHTML = 'Downloading...';

            // Call API to download reports
            fetch('/api/informed/download-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                if (data.success) {
                    outputDiv.innerHTML = 'Reports downloaded successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            if (report.success) {
                                outputDiv.innerHTML += `<li>${report.report}: Success</li>`;
                            } else {
                                outputDiv.innerHTML += `<li>${report.report}: Failed - ${report.error}</li>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after download
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to download reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Import Informed Reports button
        document.getElementById('importInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importInformedReportsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing reports to Supabase...';

            // Call API to import reports
            fetch('/api/informed/import-reports', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Reports imported successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.name}: ${report.recordCount} records imported</li>`;
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after import
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to import reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Full Informed Process button
        document.getElementById('runFullInformedProcessBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runFullInformedProcessOutput');
            const maxRetries = parseInt(document.getElementById('fullProcessMaxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('fullProcessRetryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Running full Informed process (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="fullProcessProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('fullProcessProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during processing
            const processBtn = document.getElementById('runFullInformedProcessBtn');
            processBtn.disabled = true;
            processBtn.innerHTML = 'Running...';

            // Call API to run full process
            fetch('/api/informed/run-full-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                if (data.success) {
                    outputDiv.innerHTML = 'Full process completed successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<h4>Download Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.downloadStatus}</li>`;
                            if (report.downloadError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.downloadError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';

                        outputDiv.innerHTML += '<h4>Import Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.importStatus} (${report.recordCount} records)</li>`;
                            if (report.importError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.importError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after full process
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run full process'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Complete Workflow button (Download + Import + Upload)
        document.getElementById('runCompleteWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runCompleteWorkflowOutput');

            // Get retry parameters from the same inputs as individual download button
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete end-to-end workflow...<br>';
            outputDiv.innerHTML += 'This will download reports, import them, then upload pricing data to Informed.<br>';
            outputDiv.innerHTML += `Using retry parameters: max retries=${maxRetries}, retry interval=${retryInterval}s<br><br>`;

            // Disable the button during processing
            const workflowBtn = document.getElementById('runCompleteWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Complete Workflow...';

            // Call API to run complete workflow (same as individual buttons)
            fetch('/api/informed/run-complete-workflow', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete end-to-end workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each phase
                        if (data.results.download) {
                            outputDiv.innerHTML += `<strong>Phase 1 - Download Reports:</strong> ${data.results.download.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.import) {
                            outputDiv.innerHTML += `<strong>Phase 2 - Import Reports:</strong> ${data.results.import.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Phase 3 - Upload Pricing:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.results && data.results.upload.results.upload && data.results.upload.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.results.upload.result)}<br>`;
                            }
                        }
                    }

                    // Refresh status after complete workflow
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run complete workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Phase Results:</strong><br>';
                        Object.keys(data.results).forEach(phase => {
                            if (data.results[phase]) {
                                outputDiv.innerHTML += `${phase}: ${data.results[phase].success ? 'Success' : 'Failed'}`;
                                if (data.results[phase].error) {
                                    outputDiv.innerHTML += ` - ${data.results[phase].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Enable Informed Scheduler button
        document.getElementById('enableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enabling Informed scheduler...';

            // Call API to enable scheduler
            fetch('/api/informed/enable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler enabled successfully!<br>';
                    outputDiv.innerHTML += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                    outputDiv.innerHTML += `Schedule: ${data.schedule}`;

                    // Refresh status after enabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to enable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Disable Informed Scheduler button
        document.getElementById('disableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Disabling Informed scheduler...';

            // Call API to disable scheduler
            fetch('/api/informed/disable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler disabled successfully!<br>';

                    // Refresh status after disabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to disable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Truncate tu_informed button
        document.getElementById('truncateTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('truncateTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating tu_informed table...';

            // Call API to truncate tu_informed
            fetch('/api/informed/truncate-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed truncated successfully!<br>`;
                    outputDiv.innerHTML += `Deleted ${data.deletedCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to truncate tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Fill tu_informed button
        document.getElementById('fillTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('fillTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Filling tu_informed from v_informed_upload...';

            // Call API to fill tu_informed
            fetch('/api/informed/fill-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed filled successfully!<br>`;
                    outputDiv.innerHTML += `Inserted ${data.recordCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to fill tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Export CSV button
        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('exportCsvOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Generating CSV export from tu_informed...';

            // Call API to export CSV
            fetch('/api/informed/export-csv', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV export generated successfully!<br>`;
                    const lines = data.csvContent.split('\n').length - 1;
                    outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;

                    // Store CSV content for upload step
                    window.lastGeneratedCsv = data.csvContent;

                    // Show preview of first few lines
                    const previewLines = data.csvContent.split('\n').slice(0, 5);
                    outputDiv.innerHTML += '<br><strong>Preview:</strong><br>';
                    outputDiv.innerHTML += '<pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 150px; overflow-y: auto;">';
                    outputDiv.innerHTML += previewLines.join('\n');
                    if (lines > 4) {
                        outputDiv.innerHTML += '\n... and ' + (lines - 4) + ' more rows';
                    }
                    outputDiv.innerHTML += '</pre>';
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to export CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Upload CSV button
        document.getElementById('uploadCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('uploadCsvOutput');
            outputDiv.style.display = 'block';

            // Check if we have CSV content from the export step
            if (!window.lastGeneratedCsv) {
                outputDiv.innerHTML = 'Error: No CSV content available. Please run "Export CSV" first.<br>';
                return;
            }

            outputDiv.innerHTML = 'Uploading CSV to Informed Repricer...';

            // Call API to upload CSV
            fetch('/api/informed/upload-csv', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    csvContent: window.lastGeneratedCsv
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`HTTP ${response.status}: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV uploaded to Informed successfully!<br>`;
                    if (data.result) {
                        outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.result)}<br>`;
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to upload CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Upload Workflow button
        document.getElementById('runUploadWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runUploadWorkflowOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete upload workflow...<br>';
            outputDiv.innerHTML += 'This will truncate tu_informed, fill it with fresh data, generate CSV, and upload to Informed.<br><br>';

            // Disable the button during processing
            const workflowBtn = document.getElementById('runUploadWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Workflow...';

            // Call API to run complete workflow
            fetch('/api/informed/run-upload-workflow', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete upload workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each step
                        if (data.results.truncate) {
                            outputDiv.innerHTML += `<strong>Step 1 - Truncate:</strong> ${data.results.truncate.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.truncate.deletedCount !== undefined) {
                                outputDiv.innerHTML += `Deleted ${data.results.truncate.deletedCount} records.<br>`;
                            }
                        }

                        if (data.results.fill) {
                            outputDiv.innerHTML += `<strong>Step 2 - Fill:</strong> ${data.results.fill.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.fill.recordCount !== undefined) {
                                outputDiv.innerHTML += `Inserted ${data.results.fill.recordCount} records.<br>`;
                            }
                        }

                        if (data.results.export) {
                            outputDiv.innerHTML += `<strong>Step 3 - Export:</strong> ${data.results.export.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.export.csvContent) {
                                const lines = data.results.export.csvContent.split('\n').length - 1;
                                outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;
                            }
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Step 4 - Upload:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.result)}<br>`;
                            }
                        }
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run upload workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Step Results:</strong><br>';
                        Object.keys(data.results).forEach(step => {
                            if (data.results[step]) {
                                outputDiv.innerHTML += `${step}: ${data.results[step].success ? 'Success' : 'Failed'}`;
                                if (data.results[step].error) {
                                    outputDiv.innerHTML += ` - ${data.results[step].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Amazon FBA Import button
        document.getElementById('run-amazon-import').addEventListener('click', function() {
            const outputDiv = document.getElementById('amazon-import-result');
            const button = document.getElementById('run-amazon-import');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Running import...';
            outputDiv.innerHTML = '<div class="alert alert-info">Import in progress. This may take a few minutes...</div>';

            // Call API to import Amazon FBA data
            fetch('/api/import-amazon-fba', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Import Successful!</h4>
                            <p>Imported ${data.importCount || 0} records into the database.</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Import Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Import Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Carrying Costs Refresh button
        document.getElementById('refresh-fba-carrying-costs').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-carrying-costs-result');
            const button = document.getElementById('refresh-fba-carrying-costs');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Refreshing...';
            outputDiv.innerHTML = '<div class="alert alert-info">Refreshing materialized view. This may take a few minutes...</div>';

            // Call API to refresh FBA carrying costs
            fetch('/api/refresh-fba-carrying-costs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Refresh Successful!</h4>
                            <p>Materialized view refreshed successfully.</p>
                            <p><strong>Records processed:</strong> ${data.recordCount || 'Unknown'}</p>
                            <p><strong>Duration:</strong> ${data.duration || 'Unknown'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Refresh Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Refresh Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Inventory Report Generation button
        document.getElementById('generate-fba-inventory-report').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-inventory-report-result');
            const button = document.getElementById('generate-fba-inventory-report');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating report...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating FBA inventory value report. This may take several minutes for large datasets...</div>';

            // Call API to generate FBA inventory report
            fetch('/api/generate-fba-inventory-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Report Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Report Month:</strong> ${data.reportMonth || 'Unknown'}</p>
                                    <p><strong>Records Processed:</strong> ${data.processedRecords || 'Unknown'}</p>
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Total Units:</strong> ${data.totalUnits?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Total Value:</strong> $${data.totalValue?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Avg Unit Cost:</strong> $${data.weightedAvgCost || 'Unknown'}</p>
                                </div>
                            </div>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Report Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Report Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Historical FBA Reports Generation button
        document.getElementById('generate-historical-fba-reports').addEventListener('click', function() {
            const outputDiv = document.getElementById('historical-fba-reports-result');
            const button = document.getElementById('generate-historical-fba-reports');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating historical reports...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating historical FBA inventory value reports for all months. This may take several minutes for large datasets...</div>';

            // Call API to generate historical FBA reports
            fetch('/api/generate-historical-fba-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Historical Reports Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Months Processed:</strong> ${data.monthsProcessed || 0}</p>
                                    <p><strong>Months Locked (updated _new columns):</strong> ${data.monthsLocked || 0}</p>
                                    <p><strong>Total Records Processed:</strong> ${data.totalRecordsProcessed?.toLocaleString() || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                    <p><strong>Status:</strong> Complete</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h5>Month-by-Month Details:</h5>
                                <pre style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${data.details || 'No details available'}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Historical Reports Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Historical Reports Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Import Amazon Fulfilled Inventory Report button
        document.getElementById('import-fulfilled-inventory').addEventListener('click', function() {
            const outputDiv = document.getElementById('fulfilled-inventory-import-result');
            const button = document.getElementById('import-fulfilled-inventory');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Importing...';
            outputDiv.innerHTML = '<div class="alert alert-info">Importing Amazon Fulfilled Inventory Report. This will truncate and replace existing data...</div>';

            // Call API to import fulfilled inventory
            fetch('/api/import-fulfilled-inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Import Fulfilled Inventory Report';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Import Successful!</h4>
                            <p><strong>Records Imported:</strong> ${data.importCount?.toLocaleString() || 'Unknown'}</p>
                            <p><strong>Records Skipped:</strong> ${data.skippedCount || 0}</p>
                            <p><strong>Import Type:</strong> Truncate and replace</p>
                            <div class="mt-3">
                                <h5>Details:</h5>
                                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${data.details || 'No details available'}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Import Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Import Fulfilled Inventory Report';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Import Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and the file exists in the correct location.</p>
                    </div>
                `;
            });
        });

        // Update Keepa Best Sellers Ranks button
        document.getElementById('update-keepa-ranks').addEventListener('click', function() {
            const outputDiv = document.getElementById('keepa-ranks-result');
            const button = document.getElementById('update-keepa-ranks');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Updating ranks...';
            outputDiv.innerHTML = '<div class="alert alert-info">Fetching Best Sellers data from Keepa API and updating ranks. This may take 2-3 minutes...</div>';

            // Call API to update Keepa ranks
            fetch('/api/update-keepa-ranks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Update Keepa Best Sellers Ranks';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Keepa Ranks Updated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Total ASINs Processed:</strong> ${data.totalProcessed?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Successfully Updated:</strong> ${data.updatedCount?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>New Competitor ASINs Added:</strong> ${data.newAsinCount?.toLocaleString() || 0}</p>
                                    <p><strong>Not Found in Database:</strong> ${data.notFoundCount?.toLocaleString() || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Category ID:</strong> ${data.categoryId || 'Unknown'}</p>
                                    <p><strong>Keepa Tokens Used:</strong> ${data.tokensConsumed || 'Unknown'}</p>
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <p><strong>Update Timestamp:</strong> ${data.updateTimestamp || 'Unknown'}</p>
                                <p class="text-muted">Sales ranks have been updated with current Best Sellers positions (1-5000). New competitor products have been automatically added to your database for market tracking.</p>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Keepa Ranks Update Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Update Keepa Best Sellers Ranks';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Keepa Ranks Update Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and you have sufficient Keepa API tokens.</p>
                    </div>
                `;
            });
        });

        // Generate FBA Ship-In Recommendations button
        document.getElementById('generate-fba-recommendations').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-recommendations-result');
            const button = document.getElementById('generate-fba-recommendations');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating recommendations...';
            outputDiv.innerHTML = '<div class="alert alert-info">Analyzing FBA products and generating ship-in recommendations based on sales rank data. This may take 1-2 minutes...</div>';

            // Call API to generate FBA recommendations
            fetch('/api/generate-fba-recommendations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Ship-In Recommendations';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>FBA Recommendations Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Total FBA Products Analyzed:</strong> ${data.totalAnalyzed?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Records Processed:</strong> ${data.recordsProcessed?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>New Records Created:</strong> ${data.createdCount?.toLocaleString() || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Existing Records Updated:</strong> ${data.updatedCount?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                    <p><strong>Errors Encountered:</strong> ${data.errorCount || 0}</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h5>Recommendation Breakdown:</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>5 Units (Rank &lt; 300):</strong> ${data.breakdown?.rank_300 || 0} products</p>
                                        <p><strong>4 Units (Rank &lt; 600):</strong> ${data.breakdown?.rank_600 || 0} products</p>
                                        <p><strong>3 Units (Rank &lt; 1000):</strong> ${data.breakdown?.rank_1000 || 0} products</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>2 Units (Rank &lt; 1500):</strong> ${data.breakdown?.rank_1500 || 0} products</p>
                                        <p><strong>1 Unit (Rank &lt; 2130):</strong> ${data.breakdown?.rank_2130 || 0} products</p>
                                        <p><strong>Skipped (Rank &gt; 2129):</strong> ${data.breakdown?.skipped || 0} products</p>
                                    </div>
                                </div>
                                <p class="text-muted mt-2">Recommendations are now available in the import_table_amaz_fba_inv_rpt table for FBA planning.</p>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>FBA Recommendations Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Ship-In Recommendations';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>FBA Recommendations Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and the database is accessible.</p>
                    </div>
                `;
            });
        });

        // Reset FBM Inactive Records button
        document.getElementById('reset-fbm-inactive-records').addEventListener('click', function() {
            const outputDiv = document.getElementById('reset-fbm-inactive-result');
            const button = document.getElementById('reset-fbm-inactive-records');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Resetting records...';
            outputDiv.innerHTML = '<div class="alert alert-info">Resetting FBM inactive records. This may take a few moments...</div>';

            // Call API to reset FBM inactive records
            fetch('/api/reset-fbm-inactive-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = '🔄 Reset FBM Inactive Records';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Reset Successful!</h4>
                            <p><strong>Records Found:</strong> ${data.recordsFound || 0}</p>
                            <p><strong>Records Updated:</strong> ${data.recordsUpdated || 0}</p>
                            <div class="mt-3">
                                <h5>Changes Applied:</h5>
                                <ul>
                                    <li><code>fbm_uploaded_at</code> set to <strong>NULL</strong></li>
                                    <li><code>min_weight</code> set to <strong>1</strong></li>
                                    <li><code>max_weight</code> set to <strong>2</strong></li>
                                </ul>
                            </div>
                            <p class="text-muted mt-2">These records can now be re-processed through the normal FBM workflow.</p>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Reset Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            ${data.recordsFound !== undefined ? `<p><strong>Records Found:</strong> ${data.recordsFound}</p>` : ''}
                            ${data.viewStructure ? `<p><strong>View Structure:</strong> ${JSON.stringify(data.viewStructure)}</p>` : ''}
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = '🔄 Reset FBM Inactive Records';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Reset Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and the view v_sdasins_fbm_inv0_mps_inactive exists.</p>
                    </div>
                `;
            });
        });

        // Reset FBA Inactive Records button
        document.getElementById('reset-fba-inactive-records').addEventListener('click', function() {
            const outputDiv = document.getElementById('reset-fba-inactive-result');
            const button = document.getElementById('reset-fba-inactive-records');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Resetting records...';
            outputDiv.innerHTML = '<div class="alert alert-info">Resetting FBA inactive records. This may take a few moments...</div>';

            // Call API to reset FBA inactive records
            fetch('/api/reset-fba-inactive-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = '🚫 Reset FBA Inactive Records';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Reset Successful!</h4>
                            <p><strong>Records Found:</strong> ${data.recordsFound || 0}</p>
                            <p><strong>Records Updated:</strong> ${data.recordsUpdated || 0}</p>
                            <div class="mt-3">
                                <h5>Changes Applied:</h5>
                                <ul>
                                    <li><code>fba</code> set to <strong>"N"</strong></li>
                                    <li><code>fbafnsku</code> set to <strong>NULL</strong></li>
                                    <li><code>fba_uploaded_at</code> set to <strong>NULL</strong></li>
                                    <li><code>min_weight</code> set to <strong>1</strong></li>
                                    <li><code>max_weight</code> set to <strong>2</strong></li>
                                    <li><code>notes</code> prepended with discontinuation message</li>
                                </ul>
                            </div>
                            ${data.errors ? `<div class="mt-3"><h5>⚠️ Some Updates Failed:</h5><p>${data.errors.length} records had errors</p></div>` : ''}
                            <p class="text-muted mt-2">These products have been removed from FBA and marked as discontinued.</p>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Reset Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            ${data.recordsFound !== undefined ? `<p><strong>Records Found:</strong> ${data.recordsFound}</p>` : ''}
                            ${data.viewStructure ? `<p><strong>View Structure:</strong> ${JSON.stringify(data.viewStructure)}</p>` : ''}
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = '🚫 Reset FBA Inactive Records';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Reset Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running and the view v_sdasins_fba_inv0_mps_inactive exists.</p>
                    </div>
                `;
            });
        });

        // FBA Report Locks Management
        document.getElementById('refresh-fba-locks').addEventListener('click', function() {
            refreshFbaLocksList();
        });

        function refreshFbaLocksList() {
            const container = document.getElementById('fba-locks-table-container');
            container.innerHTML = '<div class="alert alert-info">Loading FBA reports...</div>';

            fetch('/api/get-fba-reports', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFbaReportsTable(data.reports);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Failed to Load Reports</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Failed to Load Reports</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        }

        function displayFbaReportsTable(reports) {
            const container = document.getElementById('fba-locks-table-container');

            if (!reports || reports.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">No FBA reports found.</div>';
                return;
            }

            let tableHtml = `
                <style>
                    .fba-table {
                        border-collapse: collapse;
                    }
                    .fba-table th, .fba-table td {
                        border: 1px solid #dee2e6 !important;
                        padding: 8px !important;
                        white-space: nowrap;
                    }
                    .fba-table th {
                        white-space: normal;
                        word-wrap: break-word;
                    }
                </style>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-bordered fba-table">
                        <thead class="table-dark">
                            <tr>
                                <th style="text-align: right;">Month</th>
                                <th style="text-align: right;">Units</th>
                                <th style="text-align: right;">Avg<br>Cost</th>
                                <th style="text-align: right;">EOM<br>Value</th>
                                <th style="text-align: right;">Units<br>(New)</th>
                                <th style="text-align: right;">Avg Cost<br>(New)</th>
                                <th style="text-align: right;">EOM Value<br>(New)</th>
                                <th style="text-align: center;">🔒</th>
                                <th style="text-align: right;">Locked<br>At</th>
                                <th style="text-align: center;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            reports.forEach(report => {
                const monthDisplay = new Date(report.month).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                });

                const isLocked = report.locked_at !== null;

                // Format main values
                const units = (report.total_ending_units || 0).toLocaleString();
                const avgCost = '$' + (report.avg_unit_cost || 0).toFixed(2);
                const eomValue = '$' + (report.sum_of_extended_values || 0).toLocaleString();

                // Format new values (show if they exist, otherwise show dash)
                const unitsNew = report.total_ending_units_new !== null ?
                    report.total_ending_units_new.toLocaleString() : '-';
                const avgCostNew = report.avg_unit_cost_new !== null ?
                    '$' + report.avg_unit_cost_new.toFixed(2) : '-';
                const eomValueNew = report.sum_of_extended_values_new !== null ?
                    '$' + report.sum_of_extended_values_new.toLocaleString() : '-';

                // Lock status icon only
                const lockIcon = isLocked ? '🔒' : '🔓';

                const lockedAtDisplay = isLocked ?
                    new Date(report.locked_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }) :
                    '-';

                const actionButton = isLocked ?
                    `<button class="btn btn-sm btn-outline-warning" onclick="unlockFbaReport('${report.month}', '${monthDisplay}')">🔓 Unlock</button>` :
                    `<button class="btn btn-sm btn-outline-danger" onclick="lockFbaReport('${report.month}', '${monthDisplay}')">🔒 Lock</button>`;

                tableHtml += `
                    <tr>
                        <td style="text-align: right;"><strong>${monthDisplay}</strong></td>
                        <td style="text-align: right;">${units}</td>
                        <td style="text-align: right;">${avgCost}</td>
                        <td style="text-align: right;">${eomValue}</td>
                        <td style="text-align: right;">${unitsNew}</td>
                        <td style="text-align: right;">${avgCostNew}</td>
                        <td style="text-align: right;">${eomValueNew}</td>
                        <td style="text-align: center;">${lockIcon}</td>
                        <td style="text-align: right;">${lockedAtDisplay}</td>
                        <td style="text-align: center;">${actionButton}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHtml;
        }

        function lockFbaReport(month, monthDisplay) {
            fetch('/api/lock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshFbaLocksList(); // Refresh the table to show updated lock status
                } else {
                    alert(`Failed to lock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error locking report: ${error.message}`);
            });
        }

        function unlockFbaReport(month, monthDisplay) {
            if (!confirm(`⚠️ WARNING: Are you sure you want to UNLOCK the report for ${monthDisplay}?\n\nThis will allow the main columns to be overwritten during future report generations.\n\nAny locked historical data will become modifiable again.`)) {
                return;
            }

            // Double confirmation for unlock
            if (!confirm(`🚨 FINAL CONFIRMATION 🚨\n\nUnlocking ${monthDisplay} will make this historical data modifiable.\n\nAre you absolutely sure you want to proceed?`)) {
                return;
            }

            fetch('/api/unlock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Successfully unlocked report for ${monthDisplay}`);
                    refreshFbaLocksList(); // Refresh the table
                } else {
                    alert(`Failed to unlock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error unlocking report: ${error.message}`);
            });
        }

        // Amazon SP-API Connection Test
        document.getElementById('test-amazon-connection').addEventListener('click', function() {
            const button = this;
            const resultDiv = document.getElementById('amazon-connection-result');

            button.disabled = true;
            button.innerHTML = 'Testing Connection...';
            resultDiv.innerHTML = '<div class="alert alert-info">Testing Amazon SP-API connection...</div>';

            fetch('/api/test-amazon-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.innerHTML = 'Test Amazon Connection';

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ Connection Successful!</h4>
                            <p>${data.message}</p>
                            ${data.marketplaces ? `<p>Found ${data.marketplaces} marketplace(s)</p>` : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>❌ Connection Failed</h4>
                            <p>${data.message || data.error}</p>
                            <details>
                                <summary>Error Details</summary>
                                <pre>${data.details || 'No additional details available'}</pre>
                            </details>
                        </div>
                    `;
                }
            })
            .catch(error => {
                button.disabled = false;
                button.innerHTML = 'Test Amazon Connection';
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ Request Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Amazon Listing Deletion
        document.getElementById('delete-amazon-listing').addEventListener('click', function() {
            const button = this;
            const resultDiv = document.getElementById('amazon-deletion-result');
            const sku = document.getElementById('amazon-sku').value.trim();
            const marketplace = document.getElementById('amazon-marketplace').value;
            const reason = document.getElementById('amazon-reason').value.trim();

            if (!sku) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Please enter a SKU to delete.</div>';
                return;
            }

            // Confirmation dialog
            if (!confirm(`⚠️ WARNING: Are you sure you want to delete the Amazon listing for SKU "${sku}"?\n\nThis action will enqueue a deletion task and cannot be easily undone.\n\nMarketplace: ${marketplace}\nReason: ${reason || 'No reason provided'}`)) {
                return;
            }

            button.disabled = true;
            button.innerHTML = 'Enqueueing Deletion...';
            resultDiv.innerHTML = '<div class="alert alert-info">Enqueueing Amazon listing deletion task...</div>';

            const requestData = {
                sku: sku,
                marketplaceIds: [marketplace],
                reason: reason || 'Manual deletion via admin interface'
            };

            fetch('/api/delete-amazon-listing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.innerHTML = 'Enqueue Listing Deletion';

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>✅ Task Enqueued Successfully!</h4>
                            <p>${data.message}</p>
                            <p><strong>Task ID:</strong> ${data.taskId}</p>
                            <p><strong>SKU:</strong> ${data.sku}</p>
                            <p><strong>Marketplace:</strong> ${data.marketplaceIds.join(', ')}</p>
                            <p><strong>Reason:</strong> ${data.reason}</p>
                            <p><em>The task will be processed by the worker daemon. Check the Worker tab for progress.</em></p>
                        </div>
                    `;

                    // Clear the form
                    document.getElementById('amazon-sku').value = '';
                    document.getElementById('amazon-reason').value = '';
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>❌ Failed to Enqueue Task</h4>
                            <p>${data.message || data.error}</p>
                            <details>
                                <summary>Error Details</summary>
                                <pre>${data.details || 'No additional details available'}</pre>
                            </details>
                        </div>
                    `;
                }
            })
            .catch(error => {
                button.disabled = false;
                button.innerHTML = 'Enqueue Listing Deletion';
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>❌ Request Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Innova tab event handlers
        document.getElementById('importInnovaDataBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Starting Innova data import...\n';

            fetch('/api/import-innova-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Import completed successfully!\n';
                    outputDiv.innerHTML += `Total records imported: ${data.totalRecords}\n`;
                    outputDiv.innerHTML += `Batch ID: ${data.batchId}\n`;
                } else {
                    outputDiv.innerHTML += 'Import failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        document.getElementById('viewInnovaDataBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading Innova data sample...\n';

            fetch('/api/view-innova-data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Sample of imported Innova data:\n\n';
                    outputDiv.innerHTML += `Total records: ${data.totalCount}\n\n`;
                    if (data.sampleData && data.sampleData.length > 0) {
                        data.sampleData.forEach((record, index) => {
                            outputDiv.innerHTML += `Record ${index + 1}:\n`;
                            outputDiv.innerHTML += `  Category: ${record.category}\n`;
                            outputDiv.innerHTML += `  Description: ${record.description}\n`;
                            outputDiv.innerHTML += `  SKU: ${record.sku}\n`;
                            outputDiv.innerHTML += `  Internal ID: ${record.internal_id}\n`;
                            outputDiv.innerHTML += `  Availability: ${record.availability}\n`;
                            outputDiv.innerHTML += `  Current Price: $${record.current_price}\n`;
                            outputDiv.innerHTML += `  Matrix Type: ${record.matrix_type}\n\n`;
                        });
                    } else {
                        outputDiv.innerHTML += 'No data found.\n';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Safe validation (report only)
        document.getElementById('validateExistingMatchesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🔍 Running SAFE validation (report only, no database changes)...\n';

            fetch('/api/validate-innova-matches', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ autoClearBrokenLinks: false })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Safe validation completed successfully!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs with vendor_internal_id: ${data.totalMatches}\n`;
                    outputDiv.innerHTML += `Valid matches: ${data.validMatches}\n`;
                    outputDiv.innerHTML += `Questionable matches: ${data.questionableMatches}\n`;
                    outputDiv.innerHTML += `Broken links found: ${data.brokenLinks}\n`;
                    outputDiv.innerHTML += `Auto-cleared: ${data.autoCleared} (NONE - safe mode)\n\n`;
                    outputDiv.innerHTML += '📊 Detailed report generated. Use "View Validation Report" to see results.\n';
                } else {
                    outputDiv.innerHTML += '❌ Validation failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Enhanced matching using relational data
        document.getElementById('enhancedMatchingBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🚀 Running ENHANCED matching using relational data...\n';

            fetch('/api/enhanced-innova-matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Enhanced matching completed successfully!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs processed: ${data.totalProcessed}\n`;
                    outputDiv.innerHTML += `🟢 Perfect matches: ${data.perfectMatches} (${Math.round(data.perfectMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🟡 Good matches: ${data.goodMatches} (${Math.round(data.goodMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🟠 Partial matches: ${data.partialMatches} (${Math.round(data.partialMatches/data.totalProcessed*100)}%)\n`;
                    outputDiv.innerHTML += `🔴 Poor matches: ${data.poorMatches} (${Math.round(data.poorMatches/data.totalProcessed*100)}%)\n\n`;
                    outputDiv.innerHTML += '📊 Enhanced report generated. Use "View Validation Report" to see detailed results.\n';

                    // Show table with enhanced results
                    if (data.enhancedResults && data.enhancedResults.length > 0) {
                        tableContainer.style.display = 'block';
                        populateEnhancedTable(data.enhancedResults);
                    }
                } else {
                    outputDiv.innerHTML += '❌ Enhanced matching failed: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Validation with auto-clear (requires confirmation)
        document.getElementById('validateWithClearBtn').addEventListener('click', function() {
            const confirmed = confirm(
                '⚠️ WARNING: This will automatically clear broken Innova links!\n\n' +
                'This action will:\n' +
                '• Find OSLs with vendor_id=2 (Innova)\n' +
                '• Clear vendor_internal_id for broken links\n' +
                '• Only affect Innova records\n\n' +
                'Are you sure you want to proceed?'
            );

            if (!confirmed) {
                return;
            }

            const outputDiv = document.getElementById('innovaOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '⚠️ Running validation with AUTO-CLEAR enabled...\n';

            fetch('/api/validate-innova-matches', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ autoClearBrokenLinks: true })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Validation with auto-clear completed!\n\n';
                    outputDiv.innerHTML += `Total Innova OSLs processed: ${data.totalMatches}\n`;
                    outputDiv.innerHTML += `Valid matches: ${data.validMatches}\n`;
                    outputDiv.innerHTML += `Questionable matches: ${data.questionableMatches}\n`;
                    outputDiv.innerHTML += `Broken links found: ${data.brokenLinks}\n`;
                    outputDiv.innerHTML += `🗑️ Auto-cleared broken links: ${data.autoCleared}\n\n`;
                    outputDiv.innerHTML += '📊 Detailed report generated. Use "View Validation Report" to see results.\n';
                } else {
                    outputDiv.innerHTML += '❌ Validation failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        document.getElementById('viewValidationReportBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('innovaOutput');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading validation report...\n';

            fetch('/api/enhanced-validation-report')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Enhanced Validation Report: ${data.reportData.length} records loaded\n`;

                    if (data.reportData && data.reportData.length > 0) {
                        // Show table and populate it with enhanced data
                        tableContainer.style.display = 'block';
                        populateEnhancedTable(data.reportData);
                    } else {
                        outputDiv.innerHTML += 'No enhanced validation data found. Run Enhanced Matching first.\n';
                        tableContainer.style.display = 'none';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Phase 2 matching for unmatched OSLs
        document.getElementById('phase2MatchingBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('phase2Output');
            const skipCount = parseInt(document.getElementById('phase2SkipCount').value) || 0;
            const batchSize = parseInt(document.getElementById('phase2BatchSize').value) || 300;
            const maxCandidates = parseInt(document.getElementById('phase2MaxCandidates').value) || 2;
            const minConfidence = parseInt(document.getElementById('phase2MinConfidence').value) || 60;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `🚀 Running Phase 2 matching (skip: ${skipCount}, batch: ${batchSize}, max: ${maxCandidates} candidates, min: ${minConfidence}% confidence)...\n`;

            fetch('/api/phase2-innova-matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    skipCount: skipCount,
                    batchSize: batchSize,
                    maxCandidates: maxCandidates,
                    minConfidence: minConfidence
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Phase 2 matching completed successfully!\n\n';
                    outputDiv.innerHTML += `OSLs processed: ${data.processed}\n`;
                    outputDiv.innerHTML += `Potential matches found: ${data.matches}\n`;
                    outputDiv.innerHTML += `🟢 High confidence (80%+): ${data.stats.highConfidence}\n`;
                    outputDiv.innerHTML += `🟡 Medium confidence (70-79%): ${data.stats.mediumConfidence}\n`;
                    outputDiv.innerHTML += `🟠 Low confidence (50-69%): ${data.stats.lowConfidence}\n\n`;
                    outputDiv.innerHTML += '📊 Use "View Phase 2 Report" to review and approve matches.\n';
                } else {
                    outputDiv.innerHTML += '❌ Phase 2 matching failed: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '❌ Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // View Phase 2 report
        document.getElementById('viewPhase2ReportBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('phase2Output');
            const tableContainer = document.getElementById('validationTableContainer');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading Phase 2 report...\n';

            fetch('/api/phase2-validation-report')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Phase 2 Report: ${data.reportData.length} potential matches loaded\n`;

                    if (data.reportData && data.reportData.length > 0) {
                        // Show table and populate it with Phase 2 data
                        tableContainer.style.display = 'block';
                        populatePhase2Table(data.reportData);
                    } else {
                        outputDiv.innerHTML += 'No Phase 2 data found. Run Phase 2 Matching first.\n';
                        tableContainer.style.display = 'none';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    tableContainer.style.display = 'none';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                tableContainer.style.display = 'none';
            });
        });

        // Validation table functionality
        let validationData = [];
        let filteredData = [];
        let sortColumn = '';
        let sortDirection = 'asc';
        let isEnhancedData = false; // Track whether we're using enhanced or basic data

        function populateValidationTable(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = false;
            setBasicTableHeaders();
            renderTable();
            setupTableEventListeners();

            // Hide Phase 2 buttons, show regular buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'none';
            document.getElementById('bulkRejectMatchBtn').style.display = 'none';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'none';
            document.getElementById('confirmSelectedBtn').style.display = 'inline-block';
            document.getElementById('rejectSelectedBtn').style.display = 'inline-block';
        }

        function populateEnhancedTable(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = true;
            setEnhancedTableHeaders();
            renderEnhancedTable();
            setupTableEventListeners();

            // Hide Phase 2 buttons, show regular buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'none';
            document.getElementById('bulkRejectMatchBtn').style.display = 'none';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'none';
            document.getElementById('confirmSelectedBtn').style.display = 'inline-block';
            document.getElementById('rejectSelectedBtn').style.display = 'inline-block';
        }

        function populatePhase2Table(data) {
            validationData = data;
            filteredData = [...data];
            isEnhancedData = true; // Use enhanced table format for Phase 2
            setPhase2TableHeaders();
            renderPhase2Table();
            setupTableEventListeners();

            // Show Phase 2 specific buttons
            document.getElementById('phase2BulkConnectBtn').style.display = 'inline-block';
            document.getElementById('bulkRejectMatchBtn').style.display = 'inline-block';
            document.getElementById('bulkMarkInactiveBtn').style.display = 'inline-block';
            document.getElementById('confirmSelectedBtn').style.display = 'none';
            document.getElementById('rejectSelectedBtn').style.display = 'none';
        }

        function setBasicTableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 8px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="g_code">G-Code ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight Match ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="string_similarity">Similarity % ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
            `;
        }

        function setEnhancedTableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 8px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="mps_id">MPS ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_mold">OSL Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_plastic">OSL Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_stamp">OSL Stamp ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="osl_weights">OSL Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="innova_weights">Innova Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="mold_match">Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="plastic_match">Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="weight_match">Weight ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="confidence_score">Score ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px; cursor: pointer;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 8px;">Actions</th>
            `;
        }

        function setPhase2TableHeaders() {
            document.getElementById('tableHeaders').innerHTML = `
                <th style="border: 1px solid #ddd; padding: 4px; font-size: 11px;">
                    <input type="checkbox" id="selectAllCheckbox" title="Select/Deselect All">
                </th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_id">OSL ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="candidate_rank">Rank ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="mps_id">MPS ID ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_mold">OSL Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_plastic">OSL Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_stamp">OSL Stamp ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="osl_weights">OSL Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_description">Innova Description ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_parsed_mold">Parsed Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_parsed_plastic">Parsed Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="innova_weights">Innova Weights ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="mold_match">Mold ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="plastic_match">Plastic ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="weight_match">Weight ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="confidence_score">Score ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; cursor: pointer; font-size: 11px;" data-sort="status">Status ↕</th>
                <th style="border: 1px solid #ddd; padding: 4px; font-size: 11px;">Actions</th>
            `;
        }

        function renderTable() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.g_code || 'N/A'}">${record.g_code || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">${record.string_similarity}%</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${record.status}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        ${record.status !== '🔴 BROKEN_LINK' ? `
                            <button onclick="confirmMatch(${record.osl_id})" style="background: #28a745; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">✅</button>
                            <button onclick="rejectMatch(${record.osl_id})" style="background: #dc3545; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">❌</button>
                            <button onclick="showDetails(${record.osl_id})" style="background: #17a2b8; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">🔍</button>
                        ` : `
                            <button onclick="clearBrokenLink(${record.osl_id})" style="background: #6c757d; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px;">🗑️ Clear</button>
                        `}
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function renderEnhancedTable() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right; font-weight: bold;" title="Your MPS ID">${record.mps_id || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px;" title="${record.osl_mold || 'N/A'}">${record.osl_mold || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px;" title="${record.osl_plastic || 'N/A'}">${record.osl_plastic || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 150px; overflow: hidden; text-overflow: ellipsis;" title="${record.osl_stamp || 'N/A'}">${record.osl_stamp || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center; font-weight: bold;" title="Your weight range">${record.osl_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center; font-weight: bold;" title="Innova weight range">${record.innova_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${record.mold_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.mold_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${record.plastic_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.plastic_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        <span style="padding: 2px 6px; border-radius: 3px; font-size: 11px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: right; font-weight: bold;">${record.confidence_score}%</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${record.status}</td>
                    <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                        ${record.status !== '🔴 BROKEN_LINK' ? `
                            <button onclick="confirmMatch(${record.osl_id})" title="Confirm this match as correct" style="background: #28a745; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">✅</button>
                            <button onclick="rejectMatch(${record.osl_id})" title="Reject this match and clear connection" style="background: #dc3545; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">❌</button>
                            <button onclick="showEnhancedDetails(${record.osl_id})" title="View detailed comparison" style="background: #17a2b8; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">🔍</button>
                        ` : `
                            <button onclick="clearBrokenLink(${record.osl_id})" title="Clear this broken connection" style="background: #6c757d; color: white; border: none; padding: 2px 6px; margin: 1px; border-radius: 3px; font-size: 11px; cursor: pointer;">🗑️</button>
                        `}
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function renderPhase2Table() {
            const tbody = document.getElementById('validationTableBody');
            tbody.innerHTML = '';

            filteredData.forEach(record => {
                const row = document.createElement('tr');

                // Add rank indicator styling
                const rankStyle = record.candidate_rank === 1 ? 'font-weight: bold; color: #28a745;' :
                                 record.candidate_rank === 2 ? 'color: #ffc107;' : 'color: #6c757d;';

                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <input type="checkbox" class="row-checkbox" data-osl-id="${record.osl_id}" data-innova-id="${record.innova_internal_id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-size: 11px;">
                        ${record.osl_id}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; ${rankStyle} font-size: 11px;" title="Candidate rank for this OSL">
                        #${record.candidate_rank}
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-weight: bold; font-size: 11px;" title="Your MPS ID">${record.mps_id || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_mold || 'N/A'}">${record.osl_mold || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_plastic || 'N/A'}">${record.osl_plastic || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px;" title="${record.osl_stamp || 'N/A'}">${record.osl_stamp || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-weight: bold; font-size: 11px;" title="Your weight range">${record.osl_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; font-size: 11px;" title="${record.innova_description || 'NOT FOUND'}">${record.innova_description || 'NOT FOUND'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px; ${record.mold_match ? 'background-color: #d4edda;' : 'background-color: #f8d7da;'}" title="Parsed: ${record.innova_parsed_mold} → Normalized: ${record.innova_mold_normalized}">${record.innova_parsed_mold || 'PARSE_FAILED'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; font-size: 11px; ${record.plastic_match ? 'background-color: #d4edda;' : 'background-color: #f8d7da;'}" title="Parsed: ${record.innova_parsed_plastic} → Normalized: ${record.innova_plastic_normalized}">${record.innova_parsed_plastic || 'PARSE_FAILED'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-weight: bold; font-size: 11px;" title="Innova weight range">${record.innova_weights || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${record.mold_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.mold_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${record.plastic_match ? 'background-color: #28a745; color: white;' : 'background-color: #dc3545; color: white;'}">${record.plastic_match ? '✓' : '✗'}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <span style="padding: 1px 4px; border-radius: 3px; font-size: 10px; ${getWeightMatchStyle(record.weight_match)}" title="${getWeightMatchTooltip(record.weight_match)}">${getWeightMatchText(record.weight_match)}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: right; font-weight: bold; ${getConfidenceStyle(record.confidence_score)} font-size: 11px;">${record.confidence_score}%</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 10px;">${record.status || 'N/A'}</td>
                    <td style="border: 1px solid #ddd; padding: 2px; text-align: center; font-size: 11px;">
                        <button onclick="confirmPhase2Match(${record.osl_id}, ${record.innova_internal_id})" title="Create connection to this Innova product" style="background: #28a745; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">✅</button>
                        <button onclick="rejectPhase2Match(${record.osl_id}, ${record.innova_internal_id})" title="Mark as not a match" style="background: #dc3545; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">❌</button>
                        <button onclick="updateWeightsAndConnect(${record.osl_id}, ${record.innova_internal_id}, '${record.innova_weights}', '${record.osl_weights}')" title="Update OSL weights to match Innova and create connection" style="background: #fd7e14; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">⚖️</button>
                        <button onclick="clearFromPhase2(${record.osl_id})" title="Mark OSL as verified (no match needed) - removes from Phase 2" style="background: #20c997; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🗑️</button>
                        <button onclick="markMPSInactive(${record.mps_id})" title="Mark this MPS as inactive (no longer available)" style="background: #6c757d; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🚫</button>
                        <button onclick="showPhase2Details(${record.osl_id}, '${record.innova_description.replace(/'/g, "\\'")}', ${record.confidence_score})" title="View detailed comparison" style="background: #17a2b8; color: white; border: none; padding: 1px 4px; margin: 1px; border-radius: 2px; font-size: 10px; cursor: pointer;">🔍</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateSelectedCount();
            updateSelectAllState();
        }

        function getConfidenceStyle(score) {
            if (score >= 80) return 'color: #28a745;'; // Green for high confidence
            if (score >= 70) return 'color: #ffc107;'; // Yellow for medium confidence
            return 'color: #dc3545;'; // Red for low confidence
        }

        function getWeightMatchStyle(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'background-color: #28a745; color: white;';
                case 'OVERLAP': return 'background-color: #ffc107; color: black;';
                case 'NO_OVERLAP': return 'background-color: #dc3545; color: white;';
                case 'UNPARSEABLE': return 'background-color: #6c757d; color: white;';
                case 'BROKEN_LINK': return 'background-color: #6c757d; color: white;';
                default: return 'background-color: #e9ecef; color: black;';
            }
        }

        function getWeightMatchText(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'EXACT';
                case 'OVERLAP': return 'OVERLAP';
                case 'NO_OVERLAP': return 'NO MATCH';
                case 'UNPARSEABLE': return 'UNPARSEABLE';
                case 'BROKEN_LINK': return 'BROKEN';
                default: return weightMatch;
            }
        }

        function getWeightMatchTooltip(weightMatch) {
            switch(weightMatch) {
                case 'EXACT_MATCH': return 'Weight ranges match exactly';
                case 'OVERLAP': return 'Weight ranges overlap but are not identical';
                case 'NO_OVERLAP': return 'Weight ranges do not overlap at all';
                case 'UNPARSEABLE': return 'Innova weight data cannot be parsed (e.g., "Max Weight")';
                case 'BROKEN_LINK': return 'Innova record not found';
                default: return weightMatch;
            }
        }

        function setupTableEventListeners() {
            // Sorting
            document.querySelectorAll('th[data-sort]').forEach(th => {
                th.addEventListener('click', () => {
                    const column = th.getAttribute('data-sort');
                    if (sortColumn === column) {
                        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        sortColumn = column;
                        sortDirection = 'asc';
                    }
                    sortTable();
                });
            });

            // Select All checkbox
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const isChecked = this.checked;
                    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                    updateSelectedCount();
                });
            }

            // Individual checkbox selection
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('row-checkbox')) {
                    updateSelectedCount();
                    updateSelectAllState();
                }
            });

            // Filters
            document.getElementById('applyFilters').addEventListener('click', applyFilters);
            document.getElementById('clearFilters').addEventListener('click', clearFilters);

            // Bulk actions
            document.getElementById('confirmSelectedBtn').addEventListener('click', confirmSelected);
            document.getElementById('rejectSelectedBtn').addEventListener('click', rejectSelected);
            document.getElementById('phase2BulkConnectBtn').addEventListener('click', phase2BulkConnect);
            document.getElementById('bulkMarkInactiveBtn').addEventListener('click', bulkMarkMPSInactive);
            document.getElementById('bulkRejectMatchBtn').addEventListener('click', bulkRejectMatches);

            // Quick select buttons
            document.getElementById('selectPerfectBtn').addEventListener('click', () => selectByStatus('🟢 PERFECT_MATCH'));
            document.getElementById('selectGoodBtn').addEventListener('click', () => selectByStatus('🟡 GOOD_MATCH'));
            document.getElementById('selectHighConfidenceBtn').addEventListener('click', () => selectByConfidence(70));
            document.getElementById('selectNoneBtn').addEventListener('click', selectNone);
        }

        function sortTable() {
            filteredData.sort((a, b) => {
                let aVal = a[sortColumn];
                let bVal = b[sortColumn];

                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }

                if (sortDirection === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function applyFilters() {
            const statusFilter = document.getElementById('statusFilter').value;
            const similarityFilter = parseInt(document.getElementById('similarityFilter').value) || 0;
            const confidenceFilter = document.getElementById('confidenceFilter')?.value || '';
            const stampFilter = document.getElementById('stampFilter')?.value || '';

            console.log('Applying filters:', { statusFilter, similarityFilter, confidenceFilter, stampFilter });
            console.log('Total records before filter:', validationData.length);

            filteredData = validationData.filter(record => {
                const statusMatch = !statusFilter || record.status === statusFilter;
                // Handle both enhanced matching (confidence_score) and basic validation (string_similarity)
                const scoreField = record.confidence_score !== undefined ? record.confidence_score : record.string_similarity;
                const similarityMatch = scoreField >= similarityFilter;

                // Confidence score filter (for Phase 2)
                let confidenceMatch = true;
                if (confidenceFilter && record.confidence_score !== undefined) {
                    const score = record.confidence_score;
                    if (confidenceFilter === '100' && score !== 100) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '90+' && score < 90) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '80+' && score < 80) {
                        confidenceMatch = false;
                    } else if (confidenceFilter === '70+' && score < 70) {
                        confidenceMatch = false;
                    }
                }

                // OSL Stamp filter (for Phase 2)
                const stampMatch = !stampFilter || record.osl_stamp === stampFilter;

                // Debug first few records
                if (validationData.indexOf(record) < 3) {
                    console.log(`Record ${record.osl_id}: status="${record.status}", score=${scoreField}, statusMatch=${statusMatch}, similarityMatch=${similarityMatch}`);
                }

                return statusMatch && similarityMatch && confidenceMatch && stampMatch;
            });

            console.log('Records after filter:', filteredData.length);

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('similarityFilter').value = '';
            filteredData = [...validationData];

            // Call the appropriate render function based on data type
            if (isEnhancedData) {
                // Check if this is Phase 2 data (has candidate_rank)
                if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                    renderPhase2Table();
                } else {
                    renderEnhancedTable();
                }
            } else {
                renderTable();
            }
        }

        function updateSelectedCount() {
            const selected = document.querySelectorAll('.row-checkbox:checked').length;
            const total = document.querySelectorAll('.row-checkbox').length;
            document.getElementById('selectedCount').textContent = `${selected} of ${total} selected`;
        }

        function updateSelectAllState() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            if (!selectAllCheckbox) return;

            const allCheckboxes = document.querySelectorAll('.row-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');

            if (checkedCheckboxes.length === 0) {
                // None selected
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                // All selected
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                // Some selected
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // Quick select functions
        function selectByStatus(status) {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                checkbox.checked = record && record.status === status;
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        function selectByConfidence(minScore) {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record) {
                    const score = record.confidence_score !== undefined ? record.confidence_score : record.string_similarity;
                    checkbox.checked = score >= minScore;
                }
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        function selectNone() {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
            updateSelectAllState();
        }

        // Action functions
        function confirmMatch(oslId) {
            updateMatchVerification(oslId, true);
        }

        function rejectMatch(oslId) {
            updateMatchVerification(oslId, false);
        }

        function clearBrokenLink(oslId) {
            updateMatchVerification(oslId, false);
        }

        function showDetails(oslId) {
            const record = validationData.find(r => r.osl_id === oslId);
            if (record) {
                alert(`OSL ${oslId} Details:\n\n` +
                      `G-Code: ${record.g_code}\n` +
                      `Innova Description: ${record.innova_description}\n` +
                      `Weight Match: ${record.weight_match}\n` +
                      `OSL Weights: ${record.min_weight}-${record.max_weight}\n` +
                      `Innova Weights: ${record.innova_weights}\n` +
                      `String Similarity: ${record.string_similarity}%\n` +
                      `Status: ${record.status}`);
            }
        }

        function showEnhancedDetails(oslId) {
            const record = validationData.find(r => r.osl_id === oslId);
            if (record) {
                const moldIcon = record.mold_match ? '✅' : '❌';
                const plasticIcon = record.plastic_match ? '✅' : '❌';
                const weightIcon = record.weight_match === 'EXACT_MATCH' ? '✅' : record.weight_match === 'OVERLAP' ? '⚠️' : '❌';

                alert(`OSL ${oslId} Enhanced Details:\n\n` +
                      `OSL Data:\n` +
                      `  MPS ID: ${record.mps_id || 'N/A'}\n` +
                      `  Mold: ${record.osl_mold}${record.osl_mold_normalized ? ` → ${record.osl_mold_normalized}` : ''}\n` +
                      `  Plastic: ${record.osl_plastic}${record.osl_plastic_normalized ? ` → ${record.osl_plastic_normalized}` : ''}\n` +
                      `  Stamp: ${record.osl_stamp}\n` +
                      `  Weights: ${record.osl_weights}\n` +
                      `  Color: ${record.osl_color}\n\n` +
                      `Innova Data:\n` +
                      `  Description: ${record.innova_description}\n` +
                      `  Parsed Mold: ${record.innova_parsed?.mold || 'N/A'}${record.innova_mold_normalized ? ` → ${record.innova_mold_normalized}` : ''}\n` +
                      `  Parsed Plastic: ${record.innova_parsed?.plastic || 'N/A'}${record.innova_plastic_normalized ? ` → ${record.innova_plastic_normalized}` : ''}\n` +
                      `  Weights: ${record.innova_weights}\n\n` +
                      `Matching Results:\n` +
                      `  ${moldIcon} Mold Match: ${record.mold_match}\n` +
                      `  ${plasticIcon} Plastic Match: ${record.plastic_match}\n` +
                      `  ${weightIcon} Weight Match: ${record.weight_match}\n` +
                      `    OSL: ${record.osl_weights} | Innova: ${record.innova_weights}\n` +
                      `  Confidence Score: ${record.confidence_score}%\n` +
                      `  Status: ${record.status}\n` +
                      `  Recommendation: ${record.recommendation}`);
            }
        }

        // Phase 2 specific functions
        function confirmPhase2Match(oslId, innovaInternalId) {
            // No confirmation needed - direct action
                fetch('/api/create-vendor-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: oslId,
                        vendorInternalId: innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove all rows for this OSL (all candidates)
                        const rows = document.querySelectorAll(`input[data-osl-id="${oslId}"]`);
                        rows.forEach(checkbox => {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#d4edda';
                            row.style.opacity = '0.7';
                        });

                        setTimeout(() => {
                            rows.forEach(checkbox => checkbox.closest('tr').remove());
                            // Update the data arrays
                            validationData = validationData.filter(r => r.osl_id !== oslId);
                            filteredData = filteredData.filter(r => r.osl_id !== oslId);
                            updateSelectedCount();
                        }, 1000);

                        // Connection successful - visual feedback only
                    } else {
                        alert('Error creating connection: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
        }

        function rejectPhase2Match(oslId, innovaInternalId) {
            // No confirmation needed - direct action
                fetch('/api/reject-phase2-match', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: oslId,
                        innovaInternalId: innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove this specific candidate row
                        const checkbox = document.querySelector(`input[data-osl-id="${oslId}"][data-innova-id="${innovaInternalId}"]`);
                        if (checkbox) {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#f8d7da';
                            row.style.opacity = '0.7';

                            setTimeout(() => {
                                row.remove();
                                // Update the data arrays
                                validationData = validationData.filter(r => !(r.osl_id === oslId && r.innova_internal_id === innovaInternalId));
                                filteredData = filteredData.filter(r => !(r.osl_id === oslId && r.innova_internal_id === innovaInternalId));
                                updateSelectedCount();
                            }, 1000);
                        }

                        // No additional alert - the visual feedback is sufficient
                    } else {
                        alert('Error rejecting match: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
        }

        function markMPSInactive(mpsId) {
            // No confirmation needed - direct action
            fetch('/api/mark-mps-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mpsId: mpsId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all rows for this MPS ID (all OSLs using this MPS)
                    const rows = document.querySelectorAll(`input[data-osl-id]`);
                    rows.forEach(checkbox => {
                        const oslId = parseInt(checkbox.dataset.oslId);
                        const record = validationData.find(r => r.osl_id === oslId);
                        if (record && record.mps_id === mpsId) {
                            const row = checkbox.closest('tr');
                            row.style.backgroundColor = '#f8f9fa';
                            row.style.opacity = '0.5';
                            row.style.textDecoration = 'line-through';
                        }
                    });

                    setTimeout(() => {
                        // Remove from data arrays
                        validationData = validationData.filter(r => r.mps_id !== mpsId);
                        filteredData = filteredData.filter(r => r.mps_id !== mpsId);

                        // Re-render the table to maintain current filters
                        if (isEnhancedData) {
                            // Check if this is Phase 2 data (has candidate_rank)
                            if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                                renderPhase2Table();
                            } else {
                                renderEnhancedTable();
                            }
                        } else {
                            renderTable();
                        }

                        updateSelectedCount();
                    }, 1500);
                } else {
                    alert('Error marking MPS as inactive: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function bulkMarkMPSInactive() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Get unique MPS IDs from selected records
            const mpsIds = [...new Set(selectedCheckboxes.map(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                return record ? record.mps_id : null;
            }).filter(id => id !== null))];

            if (mpsIds.length === 0) {
                alert('No valid MPS records found in selection');
                return;
            }

            // No confirmation needed - direct action
            let processedCount = 0;
            let errors = [];

            mpsIds.forEach(mpsId => {
                fetch('/api/mark-mps-inactive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mpsId: mpsId })
                })
                .then(response => response.json())
                .then(data => {
                    processedCount++;

                    if (data.success) {
                        // Remove all rows for this MPS ID
                        validationData = validationData.filter(r => r.mps_id !== mpsId);
                        filteredData = filteredData.filter(r => r.mps_id !== mpsId);
                    } else {
                        errors.push(`MPS ${mpsId}: ${data.error}`);
                    }

                    // When all requests are done, re-render table
                    if (processedCount === mpsIds.length) {
                        // Re-render the table to maintain current filters
                        if (isEnhancedData) {
                            if (validationData.length > 0 && validationData[0].candidate_rank !== undefined) {
                                renderPhase2Table();
                            } else {
                                renderEnhancedTable();
                            }
                        } else {
                            renderTable();
                        }

                        updateSelectedCount();

                        if (errors.length > 0) {
                            console.warn('Some MPS records had errors:', errors);
                        }
                    }
                })
                .catch(error => {
                    processedCount++;
                    errors.push(`MPS ${mpsId}: ${error.message}`);

                    if (processedCount === mpsIds.length) {
                        updateSelectedCount();
                        alert('Some errors occurred: ' + errors.join(', '));
                    }
                });
            });
        }

        function bulkRejectMatches() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Build rejection array from selected records
            const rejections = [];
            selectedCheckboxes.forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record && record.innova_internal_id) {
                    rejections.push({
                        oslId: oslId,
                        innovaInternalId: record.innova_internal_id
                    });
                }
            });

            if (rejections.length === 0) {
                alert('No valid matches found in selected records');
                return;
            }

            // No confirmation needed - direct action
            let processedCount = 0;
            let errors = [];

            rejections.forEach(rejection => {
                fetch('/api/reject-phase2-match', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        oslId: rejection.oslId,
                        innovaInternalId: rejection.innovaInternalId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    processedCount++;

                    if (data.success) {
                        // Remove this specific OSL-Innova combination
                        const row = document.querySelector(`input[data-osl-id="${rejection.oslId}"]`);
                        if (row) {
                            const tableRow = row.closest('tr');
                            tableRow.style.backgroundColor = '#f8d7da';
                            tableRow.style.opacity = '0.7';
                            setTimeout(() => tableRow.remove(), 1000);
                        }

                        // Remove from data arrays
                        validationData = validationData.filter(r =>
                            !(r.osl_id === rejection.oslId && r.innova_internal_id === rejection.innovaInternalId)
                        );
                        filteredData = filteredData.filter(r =>
                            !(r.osl_id === rejection.oslId && r.innova_internal_id === rejection.innovaInternalId)
                        );
                    } else {
                        errors.push(`OSL ${rejection.oslId}: ${data.error}`);
                    }

                    // When all requests are done, update count
                    if (processedCount === rejections.length) {
                        updateSelectedCount();

                        if (errors.length > 0) {
                            console.warn('Some rejections had errors:', errors);
                        }
                    }
                })
                .catch(error => {
                    processedCount++;
                    errors.push(`OSL ${rejection.oslId}: ${error.message}`);

                    if (processedCount === rejections.length) {
                        updateSelectedCount();
                        if (errors.length > 0) {
                            alert('Some errors occurred: ' + errors.join(', '));
                        }
                    }
                });
            });
        }

        function updateWeightsAndConnect(oslId, innovaInternalId, innovaWeights, oslWeights) {
            // Parse Innova weights to extract min/max
            const innovaMatch = innovaWeights.match(/(\d+)-(\d+)/);
            if (!innovaMatch) {
                alert('Cannot parse Innova weights: ' + innovaWeights);
                return;
            }

            const innovaMin = parseInt(innovaMatch[1]);
            const innovaMax = parseInt(innovaMatch[2]);

            // No confirmation needed - direct action
            fetch('/api/update-weights-and-connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId,
                    innovaInternalId: innovaInternalId,
                    newMinWeight: innovaMin,
                    newMaxWeight: innovaMax,
                    originalWeights: oslWeights,
                    innovaWeights: innovaWeights
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the row to show the change
                    const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                    if (row) {
                        row.style.backgroundColor = '#d1ecf1';
                        row.style.border = '2px solid #bee5eb';

                        // Update the weights display in the row
                        const weightCell = row.querySelector('td:nth-child(8)'); // OSL weights column
                        if (weightCell) {
                            weightCell.innerHTML = `<strong>${innovaMin}-${innovaMax}</strong> <small style="color: #6c757d;">(was ${oslWeights})</small>`;
                        }

                        // Remove the row after a delay
                        setTimeout(() => {
                            row.style.opacity = '0.7';
                            setTimeout(() => {
                                row.remove();
                                // Remove from data arrays
                                validationData = validationData.filter(r => r.osl_id !== oslId);
                                filteredData = filteredData.filter(r => r.osl_id !== oslId);
                                updateSelectedCount();
                            }, 1000);
                        }, 2000);
                    }
                } else {
                    alert('Error updating weights and connecting: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function clearFromPhase2(oslId) {
            // Mark OSL as verified without creating a vendor connection
            // This removes it from future Phase 2 matching
            fetch('/api/clear-from-phase2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all rows for this OSL ID
                    const rows = document.querySelectorAll(`input[data-osl-id="${oslId}"]`);
                    rows.forEach(checkbox => {
                        const row = checkbox.closest('tr');
                        if (row) {
                            row.style.backgroundColor = '#d1ecf1';
                            row.style.opacity = '0.7';
                            row.style.textDecoration = 'line-through';
                        }
                    });

                    setTimeout(() => {
                        // Remove from data arrays
                        validationData = validationData.filter(r => r.osl_id !== oslId);
                        filteredData = filteredData.filter(r => r.osl_id !== oslId);

                        // Remove from DOM
                        rows.forEach(checkbox => {
                            checkbox.closest('tr').remove();
                        });

                        updateSelectedCount();
                    }, 1500);
                } else {
                    alert('Error clearing from Phase 2: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function showPhase2Details(oslId, innovaDescription, confidenceScore) {
            const record = validationData.find(r => r.osl_id === oslId && r.innova_description === innovaDescription);
            if (record) {
                const moldIcon = record.mold_match ? '✅' : '❌';
                const plasticIcon = record.plastic_match ? '✅' : '❌';
                const weightIcon = record.weight_match === 'EXACT_MATCH' ? '✅' : record.weight_match === 'OVERLAP' ? '⚠️' : '❌';

                alert(`Phase 2 Match Details (Rank #${record.candidate_rank}):\n\n` +
                      `OSL Data:\n` +
                      `  OSL ID: ${record.osl_id}\n` +
                      `  MPS ID: ${record.mps_id || 'N/A'}\n` +
                      `  Mold: ${record.osl_mold}\n` +
                      `  Plastic: ${record.osl_plastic}\n` +
                      `  Stamp: ${record.osl_stamp}\n` +
                      `  Weights: ${record.osl_weights}\n` +
                      `  Color: ${record.osl_color}\n\n` +
                      `Innova Candidate:\n` +
                      `  Internal ID: ${record.innova_internal_id}\n` +
                      `  Description: ${record.innova_description}\n` +
                      `  Parsed Mold: ${record.innova_parsed?.mold || 'N/A'}\n` +
                      `  Parsed Plastic: ${record.innova_parsed?.plastic || 'N/A'}\n` +
                      `  Weights: ${record.innova_weights}\n\n` +
                      `Matching Analysis:\n` +
                      `  ${moldIcon} Mold Match: ${record.mold_match}\n` +
                      `  ${plasticIcon} Plastic Match: ${record.plastic_match}\n` +
                      `  ${weightIcon} Weight Match: ${record.weight_match}\n` +
                      `  🎯 Confidence Score: ${record.confidence_score}%\n` +
                      `  📊 String Similarity: ${record.string_similarity}%\n\n` +
                      `Recommendation: ${record.confidence_score >= 80 ? '🟢 High confidence - Good match' :
                                        record.confidence_score >= 70 ? '🟡 Medium confidence - Review carefully' :
                                        '🟠 Low confidence - Consider alternatives'}`);
            }
        }

        function confirmSelected() {
            const selected = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.dataset.oslId));
            if (selected.length === 0) {
                alert('No records selected');
                return;
            }
            if (confirm(`✅ Confirm ${selected.length} selected matches as correct?`)) {
                bulkUpdateMatches(selected, true);
            }
        }

        function rejectSelected() {
            const selected = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.dataset.oslId));
            if (selected.length === 0) {
                alert('No records selected');
                return;
            }
            if (confirm(`❌ Reject ${selected.length} selected matches?\n\nThis will clear their vendor_internal_id connections.`)) {
                bulkUpdateMatches(selected, false);
            }
        }

        function phase2BulkConnect() {
            const selectedCheckboxes = Array.from(document.querySelectorAll('.row-checkbox:checked'));
            if (selectedCheckboxes.length === 0) {
                alert('No records selected');
                return;
            }

            // Build connections array from selected Phase 2 records
            const connections = [];
            selectedCheckboxes.forEach(checkbox => {
                const oslId = parseInt(checkbox.dataset.oslId);
                const record = filteredData.find(r => r.osl_id === oslId);
                if (record && record.innova_internal_id) {
                    connections.push({
                        oslId: oslId,
                        vendorInternalId: record.innova_internal_id
                    });
                }
            });

            if (connections.length === 0) {
                alert('No valid connections found in selected records');
                return;
            }

            // No confirmation needed - direct action
            fetch('/api/phase2-bulk-connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    connections: connections
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove processed rows
                    connections.forEach(conn => {
                        const row = document.querySelector(`input[data-osl-id="${conn.oslId}"]`).closest('tr');
                        if (row) {
                            row.style.backgroundColor = '#d4edda';
                            row.style.opacity = '0.7';
                        }
                    });

                    setTimeout(() => {
                        connections.forEach(conn => {
                            const row = document.querySelector(`input[data-osl-id="${conn.oslId}"]`);
                            if (row) row.closest('tr').remove();
                        });
                        // Update the data arrays
                        const connectedOslIds = connections.map(c => c.oslId);
                        validationData = validationData.filter(r => !connectedOslIds.includes(r.osl_id));
                        filteredData = filteredData.filter(r => !connectedOslIds.includes(r.osl_id));
                        updateSelectedCount();
                    }, 1000);

                    if (data.errors && data.errors.length > 0) {
                        console.warn('Some connections had errors:', data.errors);
                    }
                } else {
                    alert('Error connecting matches: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function updateMatchVerification(oslId, confirmed) {
            fetch('/api/update-match-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslId: oslId,
                    confirmed: confirmed
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the row from the table or update its status
                    const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                    row.style.backgroundColor = confirmed ? '#d4edda' : '#f8d7da';
                    row.style.opacity = '0.7';
                    setTimeout(() => {
                        row.remove();
                        // Update the data arrays
                        validationData = validationData.filter(r => r.osl_id !== oslId);
                        filteredData = filteredData.filter(r => r.osl_id !== oslId);
                        updateSelectedCount();
                    }, 1000);
                } else {
                    alert('Error updating match: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function bulkUpdateMatches(oslIds, confirmed) {
            fetch('/api/bulk-update-match-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    oslIds: oslIds,
                    confirmed: confirmed
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove processed rows
                    oslIds.forEach(oslId => {
                        const row = document.querySelector(`input[data-osl-id="${oslId}"]`).closest('tr');
                        if (row) {
                            row.style.backgroundColor = confirmed ? '#d4edda' : '#f8d7da';
                            row.style.opacity = '0.7';
                        }
                    });

                    setTimeout(() => {
                        oslIds.forEach(oslId => {
                            const row = document.querySelector(`input[data-osl-id="${oslId}"]`);
                            if (row) row.closest('tr').remove();
                        });
                        // Update the data arrays
                        validationData = validationData.filter(r => !oslIds.includes(r.osl_id));
                        filteredData = filteredData.filter(r => !oslIds.includes(r.osl_id));
                        updateSelectedCount();
                    }, 1000);

                    alert(`Successfully processed ${data.updated} records`);
                } else {
                    alert('Error updating matches: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }



        // Discraft Workflow Functions
        function downloadDiscraftVendorFile() {
            const outputDiv = document.getElementById('discraftDownloadOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📥 Downloading Discraft vendor file...\n';
            outputDiv.innerHTML += 'Source: https://www.discgolf.discraft.com/forms/stock.xlsx\n\n';

            fetch('/api/discraft/download-vendor-file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Download completed successfully!\n\n';
                    outputDiv.innerHTML += `📁 File saved to: ${data.filePath}\n`;
                    outputDiv.innerHTML += `📊 File size: ${(data.fileSize / 1024).toFixed(1)} KB\n`;
                    outputDiv.innerHTML += `🔗 Source URL: ${data.downloadUrl}\n\n`;
                    outputDiv.innerHTML += '🎯 Ready for Step 2: Import & Calculate Matches\n';

                    // Refresh Discraft status to show updated file info
                    refreshDiscraftStatus();
                } else {
                    outputDiv.innerHTML += `❌ Download failed: ${data.error}\n`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}\n`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `❌ Error: ${error.message}\n`;
                console.error('Discraft download error:', error);
            });
        }

        function runDiscraftImport() {
            const outputDiv = document.getElementById('discraftImportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🚀 Starting Discraft vendor catalog import...\n';
            outputDiv.innerHTML += 'This will truncate existing data and import fresh catalog from discraftstock.xlsx\n\n';

            // Call API to run Discraft import
            fetch('/api/discraft/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Import completed successfully!\n\n';
                    outputDiv.innerHTML += `📊 Import Results:\n`;
                    outputDiv.innerHTML += `   • Products imported: ${data.totalProducts || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Import batch ID: ${data.batchId || 'N/A'}\n\n`;

                    // Automatically calculate MPS IDs after import
                    outputDiv.innerHTML += '🔄 Calculating MPS IDs...\n';

                    return fetch('/api/discraft/calculate-mps', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });
                } else {
                    throw new Error(data.error);
                }
            })
            .then(response => response.json())
            .then(mpsData => {
                if (mpsData.success) {
                    outputDiv.innerHTML += '✅ MPS ID calculation completed!\n\n';
                    outputDiv.innerHTML += `🎯 Matching Results:\n`;
                    outputDiv.innerHTML += `   • Successfully matched: ${mpsData.calculatedCount}\n`;
                    outputDiv.innerHTML += `   • Failed to match: ${mpsData.failedCount}\n`;
                    outputDiv.innerHTML += `   • Success rate: ${mpsData.successRate}%\n\n`;

                    if (mpsData.successRate >= 80) {
                        outputDiv.innerHTML += '🎉 Great matching success rate!\n';
                    } else {
                        outputDiv.innerHTML += '⚠️ Consider reviewing unmatched products to improve parsing\n';
                    }

                    // Refresh statistics
                    refreshDiscraftStatus();
                } else {
                    outputDiv.innerHTML += `⚠️ MPS calculation failed: ${mpsData.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `❌ Error: ${error.message}\n`;
                console.error('Discraft import error:', error);
            });
        }



        function analyzeMatching() {
            const outputDiv = document.getElementById('discraftImportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📊 Analyzing matching results...\n';

            fetch('/api/discraft/analyze-matching')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = '📊 Matching Analysis Results:\n\n';
                    outputDiv.innerHTML += `🎯 MPS Matching:\n`;
                    outputDiv.innerHTML += `   • Products with MPS ID: ${data.withMpsId}\n`;
                    outputDiv.innerHTML += `   • Products without MPS ID: ${data.withoutMpsId}\n`;
                    outputDiv.innerHTML += `   • Success rate: ${data.mpsSuccessRate}%\n\n`;

                    if (data.topUnmatched && data.topUnmatched.length > 0) {
                        outputDiv.innerHTML += `❌ Top Unmatched Products (need parsing fixes):\n`;
                        data.topUnmatched.forEach(item => {
                            outputDiv.innerHTML += `   • ${item.plastic_name} ${item.mold_name} - ${item.stamp_name} (${item.count} products)\n`;
                        });
                    }
                } else {
                    outputDiv.innerHTML = `❌ Analysis failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }



        function reviewUnmatchedDiscraftProducts() {
            const outputDiv = document.getElementById('oslMatchingOutput');
            const tableDiv = document.getElementById('unmatchedOslsTable');
            const tableBody = document.getElementById('unmatchedOslsTableBody');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🎯 Loading unmatched Discraft products...\n';
            tableDiv.style.display = 'none';

            // Hide record count initially
            const recordCountDiv = document.getElementById('tableRecordCount');
            recordCountDiv.innerHTML = '';

            fetch('/api/discraft/review-unmatched-discraft-products')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const showingCount = data.unmatchedDiscraftProducts ? data.unmatchedDiscraftProducts.length : 0;
                    const totalCount = data.totalUnmatchedDiscraftProducts || 0;

                    outputDiv.innerHTML = `🎯 Found ${totalCount} unmatched Discraft products\n`;
                    outputDiv.innerHTML += '💡 These are Discraft products that don\'t match any of your OSLs.\n';
                    outputDiv.innerHTML += 'Consider adding these to your inventory or updating your parsing logic.\n\n';

                    if (data.unmatchedDiscraftProducts && data.unmatchedDiscraftProducts.length > 0) {
                        outputDiv.innerHTML += `📋 Showing first ${showingCount} unmatched Discraft products:\n\n`;

                        // Clear existing table
                        tableBody.innerHTML = '';

                        // Create table headers
                        const headerRow = `
                            <tr style="background: #f8f9fa; font-weight: bold;">
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Plastic</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Mold</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Stamp</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Weight</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Available</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Description</th>
                            </tr>
                        `;
                        tableBody.innerHTML = headerRow;

                        // Add data rows
                        data.unmatchedDiscraftProducts.forEach((product, index) => {
                            const row = `
                                <tr style="border-bottom: 1px solid #eee;">
                                    <td style="padding: 6px; border: 1px solid #ddd;">${product.plastic_name || 'N/A'}</td>
                                    <td style="padding: 6px; border: 1px solid #ddd;">${product.mold_name || 'N/A'}</td>
                                    <td style="padding: 6px; border: 1px solid #ddd;">${product.stamp_name || 'N/A'}</td>
                                    <td style="padding: 6px; border: 1px solid #ddd;">${product.min_weight}-${product.max_weight}g</td>
                                    <td style="padding: 6px; border: 1px solid #ddd;">${product.is_currently_available ? 'Yes' : 'No'}</td>
                                    <td style="padding: 6px; border: 1px solid #ddd; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${product.vendor_description || 'N/A'}</td>
                                </tr>
                            `;
                            tableBody.innerHTML += row;
                        });

                        tableDiv.style.display = 'block';

                        // Show record count
                        recordCountDiv.innerHTML = `<div style="margin-top: 10px; font-weight: bold; color: #666;">Showing ${showingCount} of ${totalCount} total unmatched Discraft products</div>`;
                    } else {
                        outputDiv.innerHTML += '✅ No unmatched Discraft products found!\n';
                        // Hide record count when no records
                        const recordCountDiv = document.getElementById('tableRecordCount');
                        recordCountDiv.innerHTML = '';
                    }
                } else {
                    outputDiv.innerHTML = `❌ Review failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }

        function reviewUnmatchedOsls() {
            const outputDiv = document.getElementById('oslMatchingOutput');
            const tableDiv = document.getElementById('unmatchedOslsTable');
            const tableBody = document.getElementById('unmatchedOslsTableBody');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📋 Loading unmatched Discraft OSLs...\n';
            tableDiv.style.display = 'none';

            // Hide record count initially
            const recordCountDiv = document.getElementById('tableRecordCount');
            recordCountDiv.innerHTML = '';

            const showInactive = document.getElementById('showInactiveMps').checked;
            const url = `/api/discraft/review-unmatched-osls?showInactive=${showInactive}`;

            fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const showingCount = data.unmatchedOsls ? data.unmatchedOsls.length : 0;
                    const totalCount = data.totalUnmatchedOsls || 0;

                    outputDiv.innerHTML = `📋 Found ${totalCount} unmatched Discraft OSLs\n`;
                    outputDiv.innerHTML += '💡 These are your OSLs that Discraft doesn\'t seem to carry.\n';
                    outputDiv.innerHTML += 'You can mark MPS records as inactive to remove them from future matching.\n\n';

                    if (data.unmatchedOsls && data.unmatchedOsls.length > 0) {
                        tableDiv.style.display = 'block';
                        tableBody.innerHTML = '';

                        // Update record count display
                        const recordCountDiv = document.getElementById('tableRecordCount');
                        recordCountDiv.innerHTML = `Showing ${showingCount} of ${totalCount} records ${showingCount < totalCount ? '(limited to first 1,000)' : ''}`;

                        data.unmatchedOsls.forEach(osl => {
                            const mps = osl.t_mps;
                            const plastic = mps.t_plastics.plastic;
                            const mold = mps.t_molds.mold;
                            const stamp = mps.t_stamps.stamp;
                            const color = osl.color_name || 'N/A';
                            const isActive = mps.active;

                            const row = document.createElement('tr');
                            row.style.backgroundColor = isActive ? '#ffffff' : '#f8f9fa';
                            row.innerHTML = `
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.id}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.mps_id}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${plastic}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${mold}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${stamp}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${osl.min_weight}-${osl.max_weight}g</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${color}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">
                                    <span style="color: ${isActive ? '#28a745' : '#dc3545'}; font-weight: bold;">
                                        ${isActive ? '✅ Active' : '❌ Inactive'}
                                    </span>
                                </td>
                                <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">
                                    ${isActive ? `
                                        <button onclick="markMpsInactive(${osl.mps_id}, this)"
                                                title="Mark MPS as Inactive"
                                                style="background: #dc3545; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                                            🚫
                                        </button>
                                    ` : `
                                        <button onclick="markMpsActive(${osl.mps_id}, this)"
                                                title="Mark MPS as Active"
                                                style="background: #28a745; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                                            ✅
                                        </button>
                                    `}
                                </td>
                            `;
                            tableBody.appendChild(row);
                        });
                    } else {
                        outputDiv.innerHTML += '✅ No unmatched OSLs found!\n';
                        // Hide record count when no records
                        const recordCountDiv = document.getElementById('tableRecordCount');
                        recordCountDiv.innerHTML = '';
                    }
                } else {
                    outputDiv.innerHTML = `❌ Review failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }

        // Global variables for sorting
        let currentSortColumn = -1;
        let currentSortDirection = 'asc';

        function toggleInactiveMps() {
            // Refresh the table when the checkbox is toggled
            reviewUnmatchedOsls();
        }

        function sortTable(columnIndex) {
            const table = document.getElementById('unmatchedOslsTableContent');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Determine sort direction
            if (currentSortColumn === columnIndex) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortDirection = 'asc';
                currentSortColumn = columnIndex;
            }

            // Update sort indicators
            for (let i = 0; i <= 7; i++) {
                const indicator = document.getElementById(`sort-${i}`);
                if (indicator) {
                    if (i === columnIndex) {
                        indicator.textContent = currentSortDirection === 'asc' ? '↑' : '↓';
                        indicator.style.color = '#007bff';
                    } else {
                        indicator.textContent = '⇅';
                        indicator.style.color = '#666';
                    }
                }
            }

            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();

                // Handle numeric columns (OSL ID, MPS ID)
                if (columnIndex === 0 || columnIndex === 1) {
                    aValue = parseInt(aValue) || 0;
                    bValue = parseInt(bValue) || 0;
                }

                // Handle weight column (extract first number)
                if (columnIndex === 5) {
                    aValue = parseInt(aValue.split('-')[0]) || 0;
                    bValue = parseInt(bValue.split('-')[0]) || 0;
                }

                // Handle active status column
                if (columnIndex === 7) {
                    aValue = aValue.includes('Active') ? 1 : 0;
                    bValue = bValue.includes('Active') ? 1 : 0;
                }

                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return currentSortDirection === 'asc' ? aValue - bValue : bValue - aValue;
                } else {
                    const comparison = aValue.localeCompare(bValue);
                    return currentSortDirection === 'asc' ? comparison : -comparison;
                }
            });

            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }

        function markMpsInactive(mpsId, buttonElement) {
            buttonElement.disabled = true;
            buttonElement.innerHTML = '⏳ Processing...';

            fetch('/api/discraft/mark-mps-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update ALL rows with this MPS ID
                    updateAllRowsWithMpsId(mpsId, false);
                } else {
                    alert(`❌ Failed to mark MPS as inactive: ${data.error}`);
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = '🚫 Mark Inactive';
                }
            })
            .catch(error => {
                alert(`❌ Error: ${error.message}`);
                buttonElement.disabled = false;
                buttonElement.innerHTML = '🚫 Mark Inactive';
            });
        }

        function markMpsActive(mpsId, buttonElement) {
            buttonElement.disabled = true;
            buttonElement.innerHTML = '⏳ Processing...';

            fetch('/api/discraft/mark-mps-active', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update ALL rows with this MPS ID
                    updateAllRowsWithMpsId(mpsId, true);
                } else {
                    alert(`❌ Failed to mark MPS as active: ${data.error}`);
                    buttonElement.disabled = false;
                    buttonElement.innerHTML = '✅ Mark Active';
                }
            })
            .catch(error => {
                alert(`❌ Error: ${error.message}`);
                buttonElement.disabled = false;
                buttonElement.innerHTML = '✅ Mark Active';
            });
        }

        function updateAllRowsWithMpsId(mpsId, isActive) {
            const tableBody = document.getElementById('unmatchedOslsTableBody');
            const rows = tableBody.querySelectorAll('tr');

            rows.forEach(row => {
                const mpsCell = row.cells[1]; // MPS ID is in column 1
                if (mpsCell && mpsCell.textContent.trim() === mpsId.toString()) {
                    // Update row background
                    row.style.backgroundColor = isActive ? '#ffffff' : '#f8f9fa';

                    // Update active status cell (column 7)
                    const activeCell = row.cells[7];
                    activeCell.innerHTML = isActive
                        ? '<span style="color: #28a745; font-weight: bold;">✅ Active</span>'
                        : '<span style="color: #dc3545; font-weight: bold;">❌ Inactive</span>';

                    // Update action button (column 8)
                    const actionCell = row.cells[8];
                    actionCell.innerHTML = isActive
                        ? `<button onclick="markMpsInactive(${mpsId}, this)"
                                   title="Mark MPS as Inactive"
                                   style="background: #dc3545; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                               🚫
                           </button>`
                        : `<button onclick="markMpsActive(${mpsId}, this)"
                                   title="Mark MPS as Active"
                                   style="background: #28a745; color: white; border: none; padding: 4px 6px; border-radius: 3px; cursor: pointer; font-size: 14px; line-height: 1;">
                               ✅
                           </button>`;
                }
            });
        }

        function exportDiscraftOrders() {
            const outputDiv = document.getElementById('discraftExportOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '📤 Starting Discraft order data export...\n';
            outputDiv.innerHTML += 'This will create a copy of the spreadsheet with order quantities from v_stats_by_osl_discraft\n\n';

            fetch('/api/discraft/export-orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '✅ Export completed successfully!\n\n';
                    outputDiv.innerHTML += `📊 Export Results:\n`;
                    outputDiv.innerHTML += `   • Output file: ${data.outputFile || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Records processed: ${data.recordsProcessed || 'N/A'}\n`;
                    outputDiv.innerHTML += `   • Order quantities updated: ${data.orderQuantitiesUpdated || 'N/A'}\n\n`;

                    if (data.outputFile) {
                        outputDiv.innerHTML += `📁 File saved to: ${data.outputFile}\n`;
                        outputDiv.innerHTML += '💡 You can now use this file for placing orders with Discraft\n';
                    }
                } else {
                    outputDiv.innerHTML += `❌ Export failed: ${data.error}\n`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}\n`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `❌ Error: ${error.message}\n`;
            });
        }

        function reviewParsingIssues() {
            const outputDiv = document.getElementById('oslMatchingOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🔧 Analyzing parsing issues...\n';

            fetch('/api/discraft/review-parsing-issues')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = '🔧 Discraft Parsing Issues Analysis:\n\n';

                    if (data.unmatchedVendorProducts && data.unmatchedVendorProducts.length > 0) {
                        outputDiv.innerHTML += `❌ Vendor Products That Don't Match Your MPS (${data.unmatchedVendorProducts.length} shown):\n`;
                        data.unmatchedVendorProducts.forEach((product, index) => {
                            outputDiv.innerHTML += `${index + 1}. "${product.plastic_name}" | "${product.mold_name}" | "${product.stamp_name}"\n`;
                            outputDiv.innerHTML += `   Available: ${product.is_currently_available ? 'Yes' : 'No'}, Weight: ${product.min_weight}-${product.max_weight}g\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    if (data.plasticMismatches && data.plasticMismatches.length > 0) {
                        outputDiv.innerHTML += `🎯 Plastic Parsing Issues:\n`;
                        data.plasticMismatches.forEach((plastic, index) => {
                            outputDiv.innerHTML += `${index + 1}. Vendor: "${plastic.vendor_plastic}" → Your DB: ${plastic.db_suggestions.join(', ') || 'No matches'}\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    if (data.moldMismatches && data.moldMismatches.length > 0) {
                        outputDiv.innerHTML += `🥏 Mold Parsing Issues:\n`;
                        data.moldMismatches.forEach((mold, index) => {
                            outputDiv.innerHTML += `${index + 1}. Vendor: "${mold.vendor_mold}" → Your DB: ${mold.db_suggestions.join(', ') || 'No matches'}\n`;
                        });
                        outputDiv.innerHTML += '\n';
                    }

                    outputDiv.innerHTML += '💡 Recommendations:\n';
                    outputDiv.innerHTML += '1. Fix parsing logic to match vendor names to your MPS names\n';
                    outputDiv.innerHTML += '2. Focus on the most common mismatches first\n';
                    outputDiv.innerHTML += '3. Consider if some vendor products are not in your catalog\n';
                } else {
                    outputDiv.innerHTML = `❌ Analysis failed: ${data.error}\n`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}\n`;
            });
        }

        function refreshDiscraftStatus() {
            const statsDiv = document.getElementById('discraftStats');
            if (!statsDiv) return;

            // Call API to get current statistics
            fetch('/api/discraft/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statsDiv.innerHTML = `
                        <h4>📊 Current Statistics</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 10px;">
                            <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #27ae60; font-size: 24px;">${data.totalProducts || 0}</strong><br>
                                <span style="color: #27ae60;">Total Products</span>
                            </div>
                            <div style="background: #e8f4fd; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #3498db; font-size: 24px;">${data.availableProducts || 0}</strong><br>
                                <span style="color: #3498db;">Available Now</span>
                            </div>
                            <div style="background: #fff3cd; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #f39c12; font-size: 24px;">${data.outOfStockProducts || 0}</strong><br>
                                <span style="color: #f39c12;">Out of Stock</span>
                            </div>
                            <div style="background: #f8d7da; padding: 10px; border-radius: 5px; text-align: center;">
                                <strong style="color: #dc3545; font-size: 24px;">${data.uniqueKeys || 0}</strong><br>
                                <span style="color: #dc3545;">Mapping Keys</span>
                            </div>
                        </div>
                        <p style="margin-top: 15px; color: #6c757d; font-style: italic;">
                            Last Import: ${data.lastImport || 'Never'}
                        </p>
                    `;
                } else {
                    statsDiv.innerHTML = '<p style="color: #dc3545;">Error loading statistics. Run import first.</p>';
                }
            })
            .catch(error => {
                statsDiv.innerHTML = '<p style="color: #dc3545;">Error loading statistics.</p>';
            });
        }

        // Discraft Scheduler Management Functions
        function loadDiscraftSchedulerStatus() {
            const statusDiv = document.getElementById('discraftSchedulerStatus');
            statusDiv.innerHTML = '<p>Loading scheduler status...</p>';

            fetch('/api/discraft/scheduler/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const isRunning = data.status === 'running';
                        const statusIcon = isRunning ? '🟢' : '🔴';
                        const statusText = isRunning ? 'Running' : 'Stopped';
                        const nextRun = data.nextRun ? new Date(data.nextRun).toLocaleString() : 'Not scheduled';

                        statusDiv.innerHTML = `
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div style="background: ${isRunning ? '#d4edda' : '#f8d7da'}; padding: 15px; border-radius: 5px; text-align: center;">
                                    <strong style="color: ${isRunning ? '#155724' : '#721c24'}; font-size: 18px;">${statusIcon} ${statusText}</strong><br>
                                    <span style="color: ${isRunning ? '#155724' : '#721c24'};">Scheduler Status</span>
                                </div>
                                <div style="background: #e2e3e5; padding: 15px; border-radius: 5px; text-align: center;">
                                    <strong style="color: #383d41; font-size: 16px;">${nextRun}</strong><br>
                                    <span style="color: #383d41;">Next Run</span>
                                </div>
                                <div style="background: #d1ecf1; padding: 15px; border-radius: 5px; text-align: center;">
                                    <strong style="color: #0c5460; font-size: 16px;">11:00 AM CST</strong><br>
                                    <span style="color: #0c5460;">Daily Schedule</span>
                                </div>
                            </div>
                            <p style="margin-top: 15px; color: #6c757d; font-style: italic;">
                                Last checked: ${new Date().toLocaleString()}
                            </p>
                        `;
                    } else {
                        statusDiv.innerHTML = '<p style="color: #dc3545;">Error loading scheduler status.</p>';
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = '<p style="color: #dc3545;">Error loading scheduler status.</p>';
                });
        }

        function updateExportThreshold() {
            const thresholdInput = document.getElementById('exportThreshold');
            const outputDiv = document.getElementById('thresholdUpdateOutput');
            const threshold = parseInt(thresholdInput.value);

            if (!threshold || threshold < 1) {
                outputDiv.style.display = 'block';
                outputDiv.innerHTML = '❌ Please enter a valid threshold (minimum 1)';
                return;
            }

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '💾 Updating export threshold...';

            fetch('/api/discraft/scheduler/threshold', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ threshold })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `✅ Export threshold updated to ${threshold} discs`;
                } else {
                    outputDiv.innerHTML = `❌ Error: ${data.error}`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `❌ Error: ${error.message}`;
            });
        }

        function startDiscraftScheduler() {
            const outputDiv = document.getElementById('discraftSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '▶️ Starting Discraft scheduler...';

            fetch('/api/discraft/scheduler/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML = '✅ Discraft scheduler started successfully';
                        loadDiscraftSchedulerStatus();
                    } else {
                        outputDiv.innerHTML = `❌ Error: ${data.error}`;
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = `❌ Error: ${error.message}`;
                });
        }

        function stopDiscraftScheduler() {
            const outputDiv = document.getElementById('discraftSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '⏹️ Stopping Discraft scheduler...';

            fetch('/api/discraft/scheduler/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML = '✅ Discraft scheduler stopped successfully';
                        loadDiscraftSchedulerStatus();
                    } else {
                        outputDiv.innerHTML = `❌ Error: ${data.error}`;
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = `❌ Error: ${error.message}`;
                });
        }

        function testDiscraftAutomation() {
            const outputDiv = document.getElementById('discraftSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '🧪 Running test automation (this may take a few minutes)...';

            fetch('/api/discraft/test-automation', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML = `✅ Test automation completed successfully!<br>
                            📊 Total discs to order: ${data.totalDiscs || 'N/A'}<br>
                            📧 Email sent: ${data.emailSent ? 'Yes' : 'No'}<br>
                            📄 Export created: ${data.exportCreated ? 'Yes' : 'No'}`;
                    } else {
                        outputDiv.innerHTML = `❌ Test failed: ${data.error}`;
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = `❌ Error: ${error.message}`;
                });
        }

        function viewDiscraftLogs() {
            const logsContainer = document.getElementById('discraftLogsContainer');
            const logsDiv = document.getElementById('discraftLogs');

            logsContainer.style.display = 'block';
            logsDiv.innerHTML = 'Loading logs...';

            fetch('/api/discraft/scheduler/logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        logsDiv.innerHTML = data.logs || 'No logs available';
                        if (document.getElementById('autoScrollLogs').checked) {
                            logsDiv.scrollTop = logsDiv.scrollHeight;
                        }
                    } else {
                        logsDiv.innerHTML = `Error loading logs: ${data.error}`;
                    }
                })
                .catch(error => {
                    logsDiv.innerHTML = `Error loading logs: ${error.message}`;
                });
        }

        function clearDiscraftLogs() {
            const logsDiv = document.getElementById('discraftLogs');
            logsDiv.innerHTML = 'Clearing logs...';

            fetch('/api/discraft/scheduler/clear-logs', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        logsDiv.innerHTML = 'Logs cleared';
                    } else {
                        logsDiv.innerHTML = `Error clearing logs: ${data.error}`;
                    }
                })
                .catch(error => {
                    logsDiv.innerHTML = `Error clearing logs: ${error.message}`;
                });
        }

        // B2F Tab Functions
        function loadB2FCount() {
            const countSpan = document.getElementById('b2fRecordCount');
            countSpan.textContent = 'Loading...';

            fetch('/api/b2f/count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        countSpan.textContent = data.count;
                    } else {
                        countSpan.textContent = 'Error';
                        console.error('Error loading B2F count:', data.error);
                    }
                })
                .catch(error => {
                    countSpan.textContent = 'Error';
                    console.error('Error loading B2F count:', error);
                });
        }

        function printB2FList() {
            const outputDiv = document.getElementById('b2fOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Generating B2F PDF list...';

            fetch('/api/b2f/generate-pdf', {
                method: 'POST'
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || 'Failed to generate PDF');
                    });
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;

                const today = new Date();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                a.download = `B2F_List_${month}-${day}.pdf`;

                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                outputDiv.innerHTML = 'PDF generated and downloaded successfully!';
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error generating PDF: ' + error.message;
            });
        }

        function downloadB2FTextList() {
            const outputDiv = document.getElementById('b2fOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Generating B2F text list...';

            fetch('/api/b2f/generate-text', {
                method: 'POST'
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || 'Failed to generate text file');
                    });
                }
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;

                const today = new Date();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                a.download = `B2F_List_${month}-${day}.txt`;

                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                outputDiv.innerHTML = 'Text file generated and downloaded successfully!';
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error generating text file: ' + error.message;
            });
        }

        function autoSelectB2Fs() {
            const outputDiv = document.getElementById('b2fOutput');
            const maxOslsInput = document.getElementById('autoSelectOslLimit');
            const autoSelectBtn = document.getElementById('autoSelectB2FsBtn');

            const maxOsls = parseInt(maxOslsInput.value) || 5;

            // Disable button and show loading
            autoSelectBtn.disabled = true;
            autoSelectBtn.innerHTML = '⏳ Processing...';

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Starting auto-selection for up to ${maxOsls} OSLs...<br>`;

            fetch('/api/b2f/auto-select', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    maxOsls: maxOsls
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += `<br><strong>✅ Auto-selection completed!</strong><br>`;
                    outputDiv.innerHTML += `📊 Summary: ${data.summary.totalOsls} OSLs processed, ${data.summary.successfulUpdates} discs moved to B2F<br><br>`;

                    if (data.selectedDiscs && data.selectedDiscs.length > 0) {
                        outputDiv.innerHTML += `<strong>📋 Selected Discs by OSL:</strong><br>`;
                        data.selectedDiscs.forEach(osl => {
                            outputDiv.innerHTML += `<br><strong>${osl.osl}</strong> (needed: ${osl.needed}, selected: ${osl.selected})<br>`;
                            osl.discs.forEach(disc => {
                                outputDiv.innerHTML += `  • ${disc.disc} (Grade: ${disc.grade})<br>`;
                            });
                        });
                    }

                    if (data.errors && data.errors.length > 0) {
                        outputDiv.innerHTML += `<br><strong>⚠️ Errors:</strong><br>`;
                        data.errors.forEach(error => {
                            outputDiv.innerHTML += `  • ${error}<br>`;
                        });
                    }

                    // Refresh the B2F count
                    loadB2FCount();
                } else {
                    outputDiv.innerHTML += `<br><strong>❌ Auto-selection failed:</strong> ${data.error}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML += `<br><strong>❌ Error:</strong> ${error.message}<br>`;
            })
            .finally(() => {
                // Re-enable button
                autoSelectBtn.disabled = false;
                autoSelectBtn.innerHTML = '🎯 Auto-Select B2Fs';
            });
        }

        // Shopify Dupes event handlers
        document.getElementById('findDuplicatesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('shopifyDupesOutput');
            const tableContainer = document.getElementById('duplicatesTableContainer');
            const deleteBtn = document.getElementById('deleteSelectedBtn');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Searching for duplicate Shopify products...';

            // Hide table and delete button initially
            tableContainer.style.display = 'none';
            deleteBtn.style.display = 'none';

            fetch('/api/shopify/find-duplicates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Found ${data.duplicates.length} duplicate products`;

                    if (data.duplicates.length > 0) {
                        displayDuplicates(data.duplicates);
                        tableContainer.style.display = 'block';
                        deleteBtn.style.display = 'inline-block';
                    } else {
                        outputDiv.innerHTML += '<br>No duplicate products found.';
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error}`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}`;
            });
        });

        document.getElementById('deleteSelectedBtn').addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('#duplicatesTableBody input[type="checkbox"]:checked');
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('Please select at least one product to delete.');
                return;
            }

            if (!confirm(`Are you sure you want to delete ${selectedIds.length} selected products? This action cannot be undone.`)) {
                return;
            }

            const outputDiv = document.getElementById('shopifyDupesOutput');
            outputDiv.innerHTML = `Deleting ${selectedIds.length} selected products...`;

            fetch('/api/shopify/delete-duplicates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ productIds: selectedIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `Successfully deleted ${data.deletedCount} products`;
                    if (data.errors && data.errors.length > 0) {
                        outputDiv.innerHTML += `<br>Errors: ${data.errors.join(', ')}`;
                    }
                    // Refresh the duplicates list
                    document.getElementById('findDuplicatesBtn').click();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error}`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}`;
            });
        });

        document.getElementById('selectAllDuplicates').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('#duplicatesTableBody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        function displayDuplicates(duplicates) {
            const tbody = document.getElementById('duplicatesTableBody');
            const countSpan = document.getElementById('duplicateCount');

            tbody.innerHTML = '';
            countSpan.textContent = `${duplicates.length} duplicates found`;

            duplicates.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">
                        <input type="checkbox" value="${product.id}">
                    </td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${product.id}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${product.title}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${product.handle}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${product.status}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${new Date(product.created_at).toLocaleDateString()}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        <a href="https://www.dzdiscs.com/products/${product.handle}" target="_blank">View</a>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // MPS Collections Cleanup Functions
        let collectionsToDelete = [];

        function previewMpsCollectionsCleanup() {
            const outputDiv = document.getElementById('mpsCollectionsPreview');
            const deleteBtn = document.getElementById('deleteMpsCollectionsBtn');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '<div class="alert alert-info">🔍 Analyzing MPS collections... This may take a few minutes.</div>';
            deleteBtn.style.display = 'none';
            collectionsToDelete = [];

            fetch('/api/mps-collections/preview-cleanup?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        collectionsToDelete = data.collections;
                        displayMpsCollectionsPreview(data.collections);
                        if (data.collections.length > 0) {
                            deleteBtn.style.display = 'inline-block';
                        }
                    } else {
                        outputDiv.innerHTML = `<div class="alert alert-error">❌ Error: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = `<div class="alert alert-error">❌ Network error: ${error.message}</div>`;
                });
        }

        function displayMpsCollectionsPreview(collections) {
            const outputDiv = document.getElementById('mpsCollectionsPreview');

            if (collections.length === 0) {
                outputDiv.innerHTML = '<div class="alert alert-info">✅ No empty MPS collections found to delete.</div>';
                return;
            }

            let html = `
                <div class="alert alert-warning">
                    <h4>⚠️ Preview: ${collections.length} Empty MPS Collections Ready for Deletion</h4>
                    <p>These collections are confirmed empty (no products) and will be permanently deleted from Shopify:</p>
                </div>
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; margin: 10px 0;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f5f5f5; position: sticky; top: 0;">
                            <tr>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Collection Title</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Handle</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Last Updated</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Shopify ID</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            collections.forEach((collection, index) => {
                const updatedDate = new Date(collection.updated_at).toLocaleDateString();
                html += `
                    <tr style="background: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                        <td style="border: 1px solid #ddd; padding: 8px; font-size: 12px;">${collection.title}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; font-size: 12px; font-family: monospace;">${collection.handle}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; font-size: 12px;">${updatedDate}</td>
                        <td style="border: 1px solid #ddd; padding: 8px; font-size: 12px; font-family: monospace;">${collection.id}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-info">
                    <h4>📝 What will happen when you click "Delete Selected Collections":</h4>
                    <ol>
                        <li><strong>Delete from Shopify:</strong> All ${collections.length} collections will be permanently removed</li>
                        <li><strong>Database Updates:</strong> Any matching t_mps records will have shopify_collection_uploaded_at set to NULL and active set to FALSE</li>
                        <li><strong>Free up space:</strong> This will reduce your collection count from 5,000 to ${5000 - collections.length}, giving you room for new collections</li>
                    </ol>
                    <p><strong>⚠️ This action cannot be undone!</strong> Make sure you want to proceed.</p>
                </div>
            `;

            outputDiv.innerHTML = html;
        }

        function confirmMpsCollectionsCleanup() {
            if (collectionsToDelete.length === 0) {
                alert('No collections selected for deletion. Please run the preview first.');
                return;
            }

            const confirmed = confirm(
                `⚠️ FINAL CONFIRMATION ⚠️\n\n` +
                `You are about to permanently delete ${collectionsToDelete.length} empty MPS collections from Shopify.\n\n` +
                `This action CANNOT be undone!\n\n` +
                `Collections to delete:\n${collectionsToDelete.map(c => `• ${c.title}`).join('\n')}\n\n` +
                `Are you absolutely sure you want to proceed?`
            );

            if (!confirmed) {
                return;
            }

            executeMpsCollectionsCleanup();
        }

        function executeMpsCollectionsCleanup() {
            const outputDiv = document.getElementById('mpsCollectionsCleanupOutput');
            const deleteBtn = document.getElementById('deleteMpsCollectionsBtn');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = '<div class="alert alert-info">🗑️ Deleting collections... This may take a few minutes.</div>';
            deleteBtn.disabled = true;

            fetch('/api/mps-collections/cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    collections: collectionsToDelete
                })
            })
            .then(response => response.json())
            .then(data => {
                displayCleanupResults(data);
                deleteBtn.disabled = false;

                // Clear the preview and hide delete button
                document.getElementById('mpsCollectionsPreview').innerHTML = '';
                deleteBtn.style.display = 'none';
                collectionsToDelete = [];
            })
            .catch(error => {
                outputDiv.innerHTML = `<div class="alert alert-error">❌ Network error: ${error.message}</div>`;
                deleteBtn.disabled = false;
            });
        }

        function displayCleanupResults(data) {
            const outputDiv = document.getElementById('mpsCollectionsCleanupOutput');

            if (data.success) {
                let html = `
                    <div class="alert alert-success">
                        <h4>✅ Cleanup Completed Successfully!</h4>
                        <p>${data.message}</p>
                    </div>
                `;

                if (data.results.success.length > 0) {
                    html += `
                        <div style="margin: 15px 0;">
                            <h4>🗑️ Successfully Deleted Collections (${data.results.success.length}):</h4>
                            <ul style="max-height: 200px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd;">
                    `;
                    data.results.success.forEach(collection => {
                        html += `<li style="margin: 5px 0; font-size: 12px;">${collection.title} (ID: ${collection.id})</li>`;
                    });
                    html += '</ul></div>';
                }

                if (data.results.dbUpdates.length > 0) {
                    html += `
                        <div style="margin: 15px 0;">
                            <h4>📝 Database Updates (${data.results.dbUpdates.length}):</h4>
                            <ul style="max-height: 150px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd;">
                    `;
                    data.results.dbUpdates.forEach(update => {
                        html += `<li style="margin: 5px 0; font-size: 12px;">MPS ID ${update.mpsId}: ${update.action}</li>`;
                    });
                    html += '</ul></div>';
                }

                if (data.results.errors.length > 0) {
                    html += `
                        <div style="margin: 15px 0;">
                            <h4>❌ Errors (${data.results.errors.length}):</h4>
                            <ul style="max-height: 150px; overflow-y: auto; background: #fff5f5; padding: 10px; border: 1px solid #e74c3c;">
                    `;
                    data.results.errors.forEach(error => {
                        html += `<li style="margin: 5px 0; font-size: 12px; color: #e74c3c;">${error.collection?.title || 'Unknown'}: ${error.error}</li>`;
                    });
                    html += '</ul></div>';
                }

                html += `
                    <div class="alert alert-info">
                        <p><strong>📊 Collection Count Update:</strong> You now have approximately ${5000 - data.results.success.length} collections (freed up ${data.results.success.length} slots)</p>
                    </div>
                `;

                outputDiv.innerHTML = html;
            } else {
                outputDiv.innerHTML = `
                    <div class="alert alert-error">
                        <h4>❌ Cleanup Failed</h4>
                        <p>${data.error}</p>
                    </div>
                `;
            }
        }


    </script>
</body>
</html>